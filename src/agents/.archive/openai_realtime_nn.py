import threading
import pyaudio
import queue
import base64
import json
import os
import time
import sys
import uuid
import wave
from pathlib import Path
from websocket import create_connection, WebSocketConnectionClosedException
from dotenv import load_dotenv
import logging

# Add the project root to the path to import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Import from the realtime directory (same as nova_sonic.py)
from src.agents.realtime.knowledgebase import knowledge_retrieval
from src.agents.realtime.prompts import PromptTemplates, InterviewInstructions, QuestionGeneration

logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

load_dotenv()

CHUNK_SIZE = 1024
RATE = 24000
FORMAT = pyaudio.paInt16
REENGAGE_DELAY_MS = 500


class Socket:
    def __init__(self, api_key, ws_url):
        self.api_key = api_key
        self.ws_url = ws_url
        self.ws = None
        self.on_msg = None
        self._stop_event = threading.Event()
        self.recv_thread = None
        self.lock = threading.Lock()

    def connect(self):
        self.ws = create_connection(self.ws_url, header=[f'Authorization: Bearer {self.api_key}', 'OpenAI-Beta: realtime=v1'])
        logging.info('Connected to WebSocket.')
        self.recv_thread = threading.Thread(target=self._receive_messages)
        self.recv_thread.start()

    def _receive_messages(self):
        while not self._stop_event.is_set():
            try:
                message = self.ws.recv()
                if message and self.on_msg:
                    self.on_msg(json.loads(message))
            except WebSocketConnectionClosedException:
                logging.error('WebSocket connection closed.')
                break
            except Exception as e:
                    logging.error(f'Error receiving message: {e}')
        logging.info('Exiting WebSocket receiving thread.')

    def send(self, data):
        try:
            with self.lock:
                if self.ws:
                    self.ws.send(json.dumps(data))
        except WebSocketConnectionClosedException:
            logging.error('WebSocket connection closed.')
        except Exception as e:
            logging.error(f'Error sending message: {e}')

    def kill(self):
        self._stop_event.set()
        if self.ws:
            try:
                self.ws.send_close()
                self.ws.close()
                logging.info('WebSocket connection closed.')
            except Exception as e:
                logging.error(f'Error closing WebSocket: {e}')
        if self.recv_thread:
            self.recv_thread.join()

class AudioIO:
    def __init__(self, chunk_size=CHUNK_SIZE, rate=RATE, format=FORMAT):
        self.chunk_size = chunk_size
        self.rate = rate
        self.format = format
        self.audio_buffer = bytearray()
        self.mic_queue = queue.Queue()
        self.mic_on_at = 0
        self.mic_active = None
        self._stop_event = threading.Event()
        self.p = pyaudio.PyAudio()
        self.recorded_audio = []  # Add audio buffer

    def _mic_callback(self, in_data, frame_count, time_info, status):
        if time.time() > self.mic_on_at:
            if not self.mic_active:
                logging.info('🎙️🟢 Mic active')
                self.mic_active = True
            self.mic_queue.put(in_data)
        else:
            if self.mic_active:
                logging.info('🎙️🔴 Mic suppressed')
                self.mic_active = False
        return (None, pyaudio.paContinue)

    def _spkr_callback(self, in_data, frame_count, time_info, status):
        bytes_needed = frame_count * 2
        current_buffer_size = len(self.audio_buffer)

        if current_buffer_size >= bytes_needed:
            audio_chunk = bytes(self.audio_buffer[:bytes_needed])
            self.audio_buffer = self.audio_buffer[bytes_needed:]
            self.mic_on_at = time.time() + REENGAGE_DELAY_MS / 1000
        else:
            audio_chunk = bytes(self.audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
            self.audio_buffer.clear()

        return (audio_chunk, pyaudio.paContinue)

    def start_streams(self):
        self.mic_stream = self.p.open(
            format=self.format,
            channels=1,
            rate=self.rate,
            input=True,
            stream_callback=self._mic_callback,
            frames_per_buffer=self.chunk_size
        )
        self.spkr_stream = self.p.open(
            format=self.format,
            channels=1,
            rate=self.rate,
            output=True,
            stream_callback=self._spkr_callback,
            frames_per_buffer=self.chunk_size
        )
        self.mic_stream.start_stream()
        self.spkr_stream.start_stream()

    def stop_streams(self):
        self.mic_stream.stop_stream()
        self.mic_stream.close()
        self.spkr_stream.stop_stream()
        self.spkr_stream.close()
        self.p.terminate()

    def start_recording(self):
        """Start recording audio"""
        self.recorded_audio = []
        logging.info("🎵 Recording started")

    def send_mic_audio(self, socket):
        while not self._stop_event.is_set():
            if not self.mic_queue.empty():
                mic_chunk = self.mic_queue.get()
                self.recorded_audio.append(mic_chunk)  # Record user audio in AudioIO
                logging.info(f'🎤 Sending {len(mic_chunk)} bytes of user audio data.')
                encoded_chunk = base64.b64encode(mic_chunk).decode('utf-8')
                socket.send({'type': 'input_audio_buffer.append', 'audio': encoded_chunk})

    def receive_audio(self, audio_chunk):
        self.audio_buffer.extend(audio_chunk)


class KnowledgeBaseManager:
    """Manages knowledge base retrieval and context injection"""
    
    def __init__(self):
        self.conversation_history = []
        self.last_query_time = 0
        self.query_cooldown = 1.0  # Minimum seconds between knowledge base queries
        
    def extract_query_from_text(self, text):
        """Extract potential query from transcribed text"""
        # Enhanced keyword-based extraction for interview context
        keywords = [
            'experience', 'skills', 'education', 'project', 'work', 'background', 
            'qualification', 'technology', 'programming', 'resume', 'cv',
            'python', 'java', 'javascript', 'ai', 'machine learning', 'deep learning',
            'langchain', 'tensorflow', 'pytorch', 'aws', 'cloud', 'database',
            'react', 'angular', 'vue', 'django', 'flask', 'spring',
            'docker', 'kubernetes', 'git', 'github', 'deployment',
            'tell me about', 'what is your', 'how do you', 'can you explain',
            'describe', 'walk me through', 'show me', 'give me an example'
        ]
        
        text_lower = text.lower()
        if any(keyword in text_lower for keyword in keywords):
            return text.strip()
        return None
    
    def should_query_knowledge_base(self, text):
        """Determine if we should query the knowledge base"""
        current_time = time.time()
        if current_time - self.last_query_time < self.query_cooldown:
            return False
            
        query = self.extract_query_from_text(text)
        return query is not None
    
    def get_context_for_query(self, query):
        """Retrieve context from knowledge base"""
        try:
            logging.info(f"🔍 Querying knowledge base for: {query}")
            result = knowledge_retrieval(query)
            
            if result['status'] == 'success' and result['contexts']:
                contexts = result['contexts']
                context_text = "\n\n".join([
                    f"Context {i+1} (Relevance: {ctx['relevance_score']:.2f}):\n{ctx['content']}"
                    for i, ctx in enumerate(contexts[:2])  # Use top 2 contexts
                ])
                logging.info(f"📚 Retrieved {len(contexts)} contexts from knowledge base")
                return context_text
            else:
                logging.info("📚 No relevant contexts found in knowledge base")
                return None
                
        except Exception as e:
            logging.error(f"❌ Error querying knowledge base: {e}")
            return None


class Realtime:
    def __init__(self, api_key, ws_url, interview_type="technical"):
        self.socket = Socket(api_key, ws_url)
        self.audio_io = AudioIO()
        self.kb_manager = KnowledgeBaseManager()
        self.interview_type = interview_type
        self.conversation_history = []
        self.transcript_buffer = []  # Add transcript buffer
        self.is_processing_text = False
        self.interview_questions = []
        self.current_question_index = 0
        self.questions_asked = []
        self.session_id = str(uuid.uuid4())[:8]  # Add session ID
        self.start_time = time.time()
        self.audio_recording_buffer = []  # Combined audio buffer
        self.user_audio_buffer = []  # User audio buffer
        self.assistant_audio_buffer = []  # Assistant audio buffer

    def start(self):
        self.socket.on_msg = self.handle_message
        self.socket.connect()

        # Load JD and Resume content for proper question generation
        jd_text = self._load_jd_content()
        resume_text = self._load_resume_content()
        
        # Generate RAG-based context for dynamic question generation (NO HARDCODED QUESTIONS)
        self._generate_interview_questions(jd_text, resume_text)
        
        # Store empty questions list - all questions will be generated dynamically using RAG
        self.interview_questions = []
        self.current_question_index = 0
        
        # Use pure RAG-based instructions - NO HARDCODED QUESTIONS
        role_type = self._extract_role_type(jd_text)
        instructions = self._generate_rag_based_instructions(role_type, jd_text, resume_text)
        
        logging.info(f"📋 Using pure RAG-based instructions for {role_type} role")
        logging.info(f"📝 All questions will be generated dynamically using knowledge base retrieval")

        # Send optimized session configuration following OpenAI Realtime API best practices
        self.socket.send({
            'type': 'session.update',
            'session': {
                'instructions': instructions,
                'input_audio_transcription': {
                    'model': 'whisper-1',
                },
                'modalities': ['audio', 'text'],
                'turn_detection': {
                    'type': 'server_vad',
                    'threshold': 0.5,
                    'prefix_padding_ms': 300,
                    'silence_duration_ms': 500
                },
                'inference_config': {
                    'max_tokens': 150,
                    'temperature': 0.7,
                    'top_p': 0.9
                },
                'voice': 'alloy',
                'language': 'en',
                'tools': [
                    {
                        'type': 'function',
                        'name': 'retrieve_knowledge',
                        'description': 'Retrieve relevant information from the knowledge base about the candidate, role requirements, or technical topics.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'query': {
                                    'type': 'string',
                                    'description': 'The query to search for in the knowledge base (e.g., "Python experience", "machine learning projects", "AWS skills")'
                                }
                            },
                            'required': ['query']
                        }
                    },
                    {
                        'type': 'function',
                        'name': 'end_interview',
                        'description': 'End the interview session gracefully when appropriate',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'reason': {
                                    'type': 'string',
                                    'description': 'Reason for ending the interview (e.g., "interview completed", "candidate requested to end")'
                                }
                            },
                            'required': ['reason']
                        }
                    }
                ]
            }
        })

        # Create response for audio output
        self.socket.send({'type': 'response.create'})
        
        audio_send_thread = threading.Thread(target=self.audio_io.send_mic_audio, args=(self.socket,))
        audio_send_thread.start()

        self.audio_io.start_streams()
        self.start_recording()  # Start recording
        
    def _load_jd_content(self):
        """Load job description content from file."""
        try:
            jd_path = "data/input/JD_Generative_AI.txt"
            if os.path.exists(jd_path):
                with open(jd_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                logging.warning(f"JD file not found at {jd_path}")
                return "Generative AI Engineer position with focus on AI/ML technologies"
        except Exception as e:
            logging.error(f"Error loading JD content: {e}")
            return "Generative AI Engineer position with focus on AI/ML technologies"
    
    def _load_resume_content(self):
        """Load resume content from file."""
        try:
            resume_path = "data/input/Resume_VarunSoni.pdf"
            if os.path.exists(resume_path):
                # For now, return a placeholder since we'd need PDF parsing
                # In a real implementation, you'd parse the PDF here
                return "Resume content available in knowledge base - contains AI/ML experience, Python, and related technologies"
            else:
                logging.warning(f"Resume file not found at {resume_path}")
                return "Resume information available in knowledge base"
        except Exception as e:
            logging.error(f"Error loading resume content: {e}")
            return "Resume information available in knowledge base"

    def _generate_interview_questions(self, jd_text, resume_text):
        """Generate RAG-based interview questions using knowledge base retrieval - NO HARDCODED QUESTIONS"""
        try:
            # Use RAG to retrieve relevant context from knowledge base for question generation
            logging.info("🔍 Retrieving relevant context from knowledge base for dynamic question generation...")
            
            # Query knowledge base for role-specific information
            role_type = self._extract_role_type(jd_text)
            role_queries = [
                f"{role_type} requirements and skills",
                f"{role_type} technical interview questions",
                f"{role_type} experience and background",
                f"{role_type} projects and technologies",
                f"{role_type} responsibilities and qualifications"
            ]
            
            retrieved_contexts = []
            for query in role_queries:
                try:
                    kb_result = knowledge_retrieval(query)
                    if kb_result['status'] == 'success' and kb_result['contexts']:
                        for ctx in kb_result['contexts'][:2]:  # Top 2 contexts per query
                            retrieved_contexts.append({
                                'content': ctx['content'],
                                'relevance': ctx['relevance_score'],
                                'query': query
                            })
                except Exception as e:
                    logging.warning(f"Knowledge base query failed for '{query}': {e}")
            
            # Combine retrieved contexts
            rag_context = "\n\n".join([
                f"Context from {ctx['query']} (Relevance: {ctx['relevance']:.2f}):\n{ctx['content']}"
                for ctx in retrieved_contexts[:6]  # Top 6 contexts
            ])
            
            logging.info(f"📚 Retrieved {len(retrieved_contexts)} contexts from knowledge base for dynamic question generation")
            
            # Return empty list - questions will be generated dynamically during conversation using RAG
            # The AI will use the retrieved context to ask relevant questions based on the conversation flow
            return []
                
        except Exception as e:
            logging.error(f"Error generating RAG-based questions: {e}")
            # NO FALLBACK - return empty list, let RAG handle everything dynamically
            return []
    
    def _extract_role_type(self, jd_text):
        """Extract the role type from the JD text (same as nova_sonic.py)"""
        if not jd_text:
            return "Software Engineer"
        
        # Look for common role keywords
        role_keywords = {
            "generative ai": "Generative AI Engineer",
            "ai engineer": "AI Engineer", 
            "machine learning": "ML Engineer",
            "data scientist": "Data Scientist",
            "software engineer": "Software Engineer",
            "backend": "Backend Engineer",
            "frontend": "Frontend Engineer",
            "full stack": "Full Stack Engineer",
            "devops": "DevOps Engineer",
            "cloud": "Cloud Engineer"
        }
        
        jd_lower = jd_text.lower()
        for keyword, role in role_keywords.items():
            if keyword in jd_lower:
                return role
        
        return "Software Engineer"  # Default

    def _generate_rag_based_instructions(self, role_type, jd_text, resume_text):
        """Generate pure RAG-based instructions with NO HARDCODED QUESTIONS"""
        
        instructions = f"""You are an expert interviewer conducting a live interview for a {role_type} position.

CRITICAL: Use the retrieve_knowledge tool to get context and generate questions dynamically.

RULES:
- Use retrieve_knowledge tool to get relevant context from knowledge base
- Generate questions based on retrieved context and conversation flow
- Ask about technologies, experience, and projects relevant to the role
- NEVER repeat questions - always generate new ones using RAG
- Keep responses brief and natural for speech
- Acknowledge what the candidate actually said, not what you think they meant

Start with: "Hello! I'm your AI interviewer for the {role_type} position. Let me get some context about this role and ask you about your relevant experience." Then use retrieve_knowledge tool."""
        
        return instructions

    def _get_rag_context_for_instructions(self, role_type, jd_text, resume_text):
        """Get RAG context from knowledge base for instructions"""
        try:
            # Query knowledge base for role-specific information
            role_queries = [
                f"{role_type} requirements and skills",
                f"{role_type} technical interview questions",
                f"{role_type} experience and background",
                f"{role_type} projects and technologies",
                f"{role_type} responsibilities and qualifications"
            ]
            
            retrieved_contexts = []
            for query in role_queries:
                try:
                    kb_result = knowledge_retrieval(query)
                    if kb_result['status'] == 'success' and kb_result['contexts']:
                        for ctx in kb_result['contexts'][:2]:  # Top 2 contexts per query
                            retrieved_contexts.append({
                                'content': ctx['content'],
                                'relevance': ctx['relevance_score'],
                                'query': query
                            })
                except Exception as e:
                    logging.warning(f"Knowledge base query failed for '{query}': {e}")
            
            # Combine retrieved contexts
            rag_context = "\n\n".join([
                f"Context from {ctx['query']} (Relevance: {ctx['relevance']:.2f}):\n{ctx['content']}"
                for ctx in retrieved_contexts[:6]  # Top 6 contexts
            ])
            
            return rag_context if rag_context else "Knowledge base context will be retrieved dynamically during the interview."
                
        except Exception as e:
            logging.error(f"Error getting RAG context for instructions: {e}")
            return "Knowledge base context will be retrieved dynamically during the interview."

    def start_recording(self):
        """Start recording audio with enhanced buffer management"""
        self.audio_io.start_recording()
        self.audio_recording_buffer = []
        self.user_audio_buffer = []
        self.assistant_audio_buffer = []
        logging.info(f"🎵 Recording started for session {self.session_id}")

    def stop_recording(self):
        """Stop recording and save both user and assistant audio separately"""
        timestamp = int(time.time())
        
        # Create output directories
        recordings_dir = f"data/outputs/recordings/{self.session_id}"
        os.makedirs(recordings_dir, exist_ok=True)
        
        # Get user audio from AudioIO
        user_audio_data = self.audio_io.recorded_audio if hasattr(self.audio_io, 'recorded_audio') else []
        
        # Save combined audio
        combined_path = f"{recordings_dir}/combined_{timestamp}.wav"
        if self.audio_recording_buffer:
            try:
                with wave.open(combined_path, 'wb') as wf:
                    wf.setnchannels(1)
                    wf.setsampwidth(self.audio_io.p.get_sample_size(FORMAT))
                    wf.setframerate(RATE)
                    wf.writeframes(b''.join(self.audio_recording_buffer))
                logging.info(f"🎵 Combined audio saved to {combined_path}")
            except Exception as e:
                logging.error(f"Error saving combined audio: {e}")
        
        # Save user audio separately
        user_path = f"{recordings_dir}/user_{timestamp}.wav"
        if user_audio_data:
            try:
                with wave.open(user_path, 'wb') as wf:
                    wf.setnchannels(1)
                    wf.setsampwidth(self.audio_io.p.get_sample_size(FORMAT))
                    wf.setframerate(RATE)
                    wf.writeframes(b''.join(user_audio_data))
                logging.info(f"🎤 User audio saved to {user_path}")
            except Exception as e:
                logging.error(f"Error saving user audio: {e}")
        
        # Save assistant audio separately
        assistant_path = f"{recordings_dir}/assistant_{timestamp}.wav"
        if self.assistant_audio_buffer:
            try:
                with wave.open(assistant_path, 'wb') as wf:
                    wf.setnchannels(1)
                    wf.setsampwidth(self.audio_io.p.get_sample_size(FORMAT))
                    wf.setframerate(RATE)
                    wf.writeframes(b''.join(self.assistant_audio_buffer))
                logging.info(f"🤖 Assistant audio saved to {assistant_path}")
            except Exception as e:
                logging.error(f"Error saving assistant audio: {e}")

    def save_transcripts(self):
        """Save comprehensive transcripts with metadata"""
        timestamp = int(time.time())
        transcripts_dir = f"data/outputs/transcripts/{self.session_id}"
        os.makedirs(transcripts_dir, exist_ok=True)
        
        # Create comprehensive transcript data
        transcript_data = {
            "session_metadata": {
                "session_id": self.session_id,
                "timestamp": timestamp,
                "interview_type": self.interview_type,
                "duration_seconds": time.time() - self.start_time,
                "total_exchanges": len(self.transcript_buffer),
                "start_time": self.start_time,
                "end_time": time.time()
            },
            "conversation": self.transcript_buffer,
            "conversation_summary": {
                "user_messages": len([msg for msg in self.transcript_buffer if msg['role'] == 'user']),
                "assistant_messages": len([msg for msg in self.transcript_buffer if msg['role'] == 'assistant']),
                "total_words": sum(len(msg['content'].split()) for msg in self.transcript_buffer),
                "average_response_length": sum(len(msg['content']) for msg in self.transcript_buffer) / len(self.transcript_buffer) if self.transcript_buffer else 0
            },
            "knowledge_base_usage": {
                "tool_calls_made": len([msg for msg in self.transcript_buffer if 'tool_call' in msg.get('content', '')]),
                "rag_queries": len([msg for msg in self.transcript_buffer if 'retrieve_knowledge' in msg.get('content', '')])
            }
        }
        
        # Save main transcript
        transcript_path = f"{transcripts_dir}/transcript_{timestamp}.json"
        try:
            with open(transcript_path, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, indent=2, ensure_ascii=False)
            logging.info(f"📝 Comprehensive transcript saved to {transcript_path}")
        except Exception as e:
            logging.error(f"Error saving transcript: {e}")
        
        # Save simple text version
        text_path = f"{transcripts_dir}/conversation_{timestamp}.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(f"Interview Session: {self.session_id}\n")
                f.write(f"Type: {self.interview_type}\n")
                f.write(f"Duration: {transcript_data['session_metadata']['duration_seconds']:.1f} seconds\n")
                f.write("="*50 + "\n\n")
                
                for msg in self.transcript_buffer:
                    role = "USER" if msg['role'] == 'user' else "AI"
                    timestamp_str = time.strftime("%H:%M:%S", time.localtime(msg['timestamp']))
                    f.write(f"[{timestamp_str}] {role}: {msg['content']}\n\n")
            
            logging.info(f"📄 Text transcript saved to {text_path}")
        except Exception as e:
            logging.error(f"Error saving text transcript: {e}")

    def handle_message(self, message):
        event_type = message.get('type')
        logging.info(f'Received message type: {event_type}')

        if event_type == 'response.audio.delta':
            audio_content = base64.b64decode(message['delta'])
            self.audio_io.receive_audio(audio_content)
            self.audio_recording_buffer.append(audio_content)  # Record combined audio
            self.assistant_audio_buffer.append(audio_content)  # Record assistant audio separately
            logging.info(f'Received {len(audio_content)} bytes of assistant audio data.')

        elif event_type == 'response.audio.done':
            logging.info('AI finished speaking.')
        
        elif event_type == 'conversation.item.input_audio_transcription.completed':
            # Handle transcribed text from user
            logging.info(f"🔍 USER TRANSCRIPTION EVENT RECEIVED")
            
            # Get the item from the message
            item = message.get('item', {})
            logging.info(f"🔍 DEBUG: Audio transcription completed: {json.dumps(item, indent=2)}")
            
            # Check if this is a user transcription
            if item.get('role') == 'user':
                transcribed_text = item.get('transcript', '')
                if not transcribed_text:
                    # Try alternative transcript locations
                    if 'transcription' in item and 'transcript' in item['transcription']:
                        transcribed_text = item['transcription']['transcript']
                    elif 'content' in item:
                        transcribed_text = item['content']
                    elif 'text' in item:
                        transcribed_text = item['text']
            
                if transcribed_text and len(transcribed_text.strip()) > 0:
                    logging.info(f'👤 User (transcription completed): {transcribed_text}')
                    
                    # Record user transcript
                    self.conversation_history.append({'role': 'user', 'content': transcribed_text})
                    self.transcript_buffer.append({
                        'role': 'user',
                        'content': transcribed_text,
                        'timestamp': time.time()
                    })  # Store user transcript
                    
                    # Process user input for RAG-based knowledge base queries
                    self.process_user_input(transcribed_text)
                    
                    # Track interview progress
                    self._update_interview_progress(transcribed_text)
                else:
                    logging.warning(f"⚠️ Empty or missing transcript in user transcription event: {message}")

        elif event_type == 'conversation.item.output_audio_transcription.completed':
            # Handle AI response transcription (for logging)
            ai_response = message.get('transcript', '')
            if not ai_response:
                # Try alternative transcript locations
                if 'item' in message and 'transcript' in message['item']:
                    ai_response = message['item']['transcript']
                elif 'transcription' in message and 'transcript' in message['transcription']:
                    ai_response = message['transcription']['transcript']
            
            if ai_response:
                logging.info(f'🤖 AI responded: {ai_response}')
                self.conversation_history.append({'role': 'assistant', 'content': ai_response})
                self.transcript_buffer.append({
                    'role': 'assistant',
                    'content': ai_response,
                    'timestamp': time.time()
                })  # Store assistant transcript

        elif event_type == 'response.tool_call':
            # Handle tool calls from the AI
            tool_call = message.get('tool_call', {})
            tool_name = tool_call.get('name', '')
            tool_call_id = tool_call.get('id', '')
            
            logging.info(f"🔧 Tool call received: {tool_name} with ID: {tool_call_id}")
            
            if tool_name == 'retrieve_knowledge':
                # Extract query from tool call
                arguments = tool_call.get('arguments', {})
                query = arguments.get('query', '')
                
                if query:
                    logging.info(f"🔍 AI requested knowledge retrieval for: {query}")
                    
                    # Record tool call in transcript
                    self.transcript_buffer.append({
                        'role': 'system',
                        'content': f"🔧 Tool Call: retrieve_knowledge(query='{query}')",
                        'timestamp': time.time()
                    })
                    
                    # Perform knowledge retrieval using the knowledge base module
                    try:
                        from src.agents.realtime.knowledgebase import knowledge_retrieval
                        kb_result = knowledge_retrieval(query)
                        
                        if kb_result['status'] == 'success' and kb_result['contexts']:
                            # Format the retrieved context
                            contexts = kb_result['contexts']
                            context_text = "\n\n".join([
                                f"Context {i+1} (Relevance: {ctx['relevance_score']:.2f}):\n{ctx['content']}"
                                for i, ctx in enumerate(contexts[:3])  # Use top 3 contexts
                            ])
                            
                            logging.info(f"📚 Retrieved {len(contexts)} contexts from knowledge base")
                            
                            # Record tool result in transcript
                            self.transcript_buffer.append({
                                'role': 'system',
                                'content': f"📚 Knowledge Retrieved: {len(contexts)} contexts found",
                                'timestamp': time.time()
                            })
                            
                            # Send tool result back to AI
                            self.socket.send({
                                'type': 'response.tool_call.result',
                                'tool_call_id': tool_call_id,
                                'content': context_text
                            })
                        else:
                            logging.info("📚 No relevant contexts found in knowledge base")
                            self.socket.send({
                                'type': 'response.tool_call.result',
                                'tool_call_id': tool_call_id,
                                'content': "No relevant information found in the knowledge base for this query."
                            })
                            
                    except Exception as e:
                        logging.error(f"❌ Error during knowledge retrieval: {e}")
                        self.socket.send({
                            'type': 'response.tool_call.result',
                            'tool_call_id': tool_call_id,
                            'content': f"Error retrieving information: {str(e)}"
                        })
                else:
                    logging.warning("⚠️ Empty query in retrieve_knowledge tool call")
                    self.socket.send({
                        'type': 'response.tool_call.result',
                        'tool_call_id': tool_call_id,
                        'content': "No query provided for knowledge retrieval."
                    })
                    
            elif tool_name == 'end_interview':
                # Handle interview end
                arguments = tool_call.get('arguments', {})
                reason = arguments.get('reason', 'Interview completed')
                
                logging.info(f"🏁 AI requested to end interview: {reason}")
                
                # Send tool result
                self.socket.send({
                    'type': 'response.tool_call.result',
                    'tool_call_id': tool_call_id,
                    'content': f"Interview ended: {reason}"
                })
                
                # Stop the session
                self.stop()
                
            else:
                logging.warning(f"⚠️ Unknown tool call: {tool_name}")
                self.socket.send({
                    'type': 'response.tool_call.result',
                    'tool_call_id': tool_call_id,
                    'content': f"Unknown tool: {tool_name}"
                })

    def process_user_input(self, text):
        """Process user input - AI will use knowledge base tools directly"""
        if self.is_processing_text:
            return
            
        self.is_processing_text = True
        
        try:
            # Just log the user input - AI will use retrieve_knowledge tool directly
            logging.info(f"📝 User input processed: {text[:100]}...")
            logging.info("💡 AI will use retrieve_knowledge tool directly for dynamic context retrieval")
                    
        except Exception as e:
            logging.error(f"Error processing user input: {e}")
        finally:
            self.is_processing_text = False

    def inject_context_to_conversation(self, context, user_query):
        """NO CONTEXT INJECTION - Let the AI use knowledge base tools directly"""
        # The AI should use the retrieve_knowledge tool directly, not receive hardcoded context
        # This method is kept for compatibility but does nothing
        logging.info("💡 AI will use retrieve_knowledge tool directly for dynamic context retrieval")

    def _update_interview_progress(self, user_response):
        """Track conversation progress for dynamic RAG-based interviews"""
        try:
            # Just log the user response for conversation tracking
            logging.info(f"📝 User response received: {user_response[:100]}...")
            logging.info(f"📊 Conversation continues with dynamic RAG-based question generation")
                
        except Exception as e:
            logging.error(f"Error updating interview progress: {e}")

    def _get_next_question_context(self):
        """NO HARDCODED QUESTIONS - AI will use knowledge base tools directly"""
        # The AI should use the retrieve_knowledge tool directly for dynamic question generation
        # This method is kept for compatibility but does nothing
        logging.info("💡 AI will use retrieve_knowledge tool directly for dynamic question generation")
        return "AI will generate questions dynamically using knowledge base tools"

    def stop(self):
        logging.info('Shutting down Realtime session.')
        self.stop_recording()  # Save audio
        self.save_transcripts()  # Save transcripts
        self.audio_io.stop_streams()
        self.socket.kill()


def main():
    api_key = os.getenv('OPENAI_API_KEY')
    ws_url = 'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01'  # Fix model ID

    if not api_key:
        logging.error("❌ OPENAI_API_KEY not found in environment variables")
        return

    # Initialize with technical interview type
    realtime = Realtime(api_key, ws_url, interview_type="technical")

    try:
        logging.info("🚀 Starting Realtime AI with Knowledge Base integration...")
        realtime.start()
        print("\n" + "="*60)
        print("🎯 SESSION ACTIVE - Press Enter to end session")
        print("="*60)
        input()  # Wait for Enter
        realtime.stop()
    except KeyboardInterrupt:
        logging.info("🛑 Interrupted by user (Ctrl+C)")
        realtime.stop()
    except Exception as e:
        logging.error(f"❌ Unexpected error: {e}")
        realtime.stop()


if __name__ == '__main__':
    main()