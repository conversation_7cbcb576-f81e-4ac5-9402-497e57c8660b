import os
import json
import base64
import pyaudio
import wave
import time
import uuid
import datetime
import signal
import logging
import threading
import queue
import select
import glob
import warnings
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional, Dict, Any
from websocket import create_connection, WebSocketConnectionClosedException

# Suppress Pydantic warnings from third-party libraries
warnings.filterwarnings("ignore", message=".*protected namespace.*")
from src.tools.knowledgebase import knowledge_retrieval
from src.tools.prompts import PromptTemplates, QuestionGeneration, InterviewInstructions
from src.tools.metrics import MetricsCollector
from core.config import Config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

# Load environment variables
load_dotenv()

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
TEMPERATURE = float(os.getenv('TEMPERATURE', 0.8))
VOICE = 'alloy'

# Audio configuration
INPUT_SAMPLE_RATE = 24000
OUTPUT_SAMPLE_RATE = 24000
CHANNELS = 1
FORMAT = pyaudio.paInt16
CHUNK_SIZE = 1024
REENGAGE_DELAY_MS = 100  # Reduced from 500ms to 100ms for better responsiveness

def load_jd_and_resume(input_dir="data/input"):
    """Load JD and Resume files from the input directory."""
    jd_text = ""
    resume_text = ""
    
    try:
        # Look for JD files
        jd_patterns = ["*JD*.txt", "*JD*.pdf", "*job*.txt", "*job*.pdf"]
        jd_file = None
        
        for pattern in jd_patterns:
            files = glob.glob(os.path.join(input_dir, pattern))
            if files:
                jd_file = files[0]
                break
        
        if jd_file and jd_file.endswith('.txt'):
            with open(jd_file, 'r', encoding='utf-8') as f:
                jd_text = f.read()
            print(f"Loaded JD from: {os.path.basename(jd_file)}")
        
        # Look for Resume files
        resume_patterns = ["*Resume*.pdf", "*resume*.pdf", "*CV*.pdf"]
        resume_file = None
        
        for pattern in resume_patterns:
            files = glob.glob(os.path.join(input_dir, pattern))
            if files:
                resume_file = files[0]
                break
        
        if resume_file:
            # Load actual resume content for proper question alignment
            try:
                # Use the knowledge base system to extract resume content
                from src.tools.knowledgebase import create_kb_from_pdf
                resume_text = create_kb_from_pdf(resume_file)
                print(f"Loaded Resume content from: {os.path.basename(resume_file)}")
            except Exception as e:
                print(f"Error loading resume content: {e}")
            resume_text = f"Resume loaded from: {os.path.basename(resume_file)}"
        
        return jd_text, resume_text
        
    except Exception as e:
        print(f"Error loading JD/Resume files: {e}")
        return "", ""

class Socket:
    """WebSocket connection handler."""
    
    def __init__(self, api_key, ws_url, on_msg=None):
        self.api_key = api_key
        self.ws_url = ws_url
        self.ws = None
        self.on_msg = on_msg
        self.send_queue = queue.Queue()
        self._stop_event = threading.Event()
        self.loop_thread = None

    def connect(self):
        """Connect to WebSocket and start main loop."""
        try:
            self.ws = create_connection(
                self.ws_url, 
                header=[
            f'Authorization: Bearer {self.api_key}', 
            'OpenAI-Beta: realtime=v1'
                ],
                timeout=10  # Add timeout like the working version
            )
            logging.info('✅ Connected to WebSocket successfully.')
            logging.info(f'🔗 WebSocket URL: {self.ws_url}')
        except Exception as e:
            logging.error(f'❌ Failed to connect to WebSocket: {e}')
            raise

        # Start a unified loop for sending and receiving messages
        self.loop_thread = threading.Thread(target=self._socket_loop)
        self.loop_thread.start()

    def _socket_loop(self):
        """Main WebSocket loop for sending and receiving messages."""
        logging.info('🔄 Starting WebSocket loop...')
        message_count = 0
        
        while not self._stop_event.is_set():
            try:
                # Check if there's a message in the queue to send first
                try:
                    outgoing_message = self.send_queue.get_nowait()
                    self.ws.send(json.dumps(outgoing_message))
                    logging.info(f'📤 Sent message: {outgoing_message.get("type", "unknown")}')
                except queue.Empty:
                    pass  # No message to send, continue to receive

                # Try to receive a message (non-blocking with timeout)
                try:
                    self.ws.settimeout(0.1)  # Set timeout like the working version
                    message = self.ws.recv()
                    if message and self.on_msg:
                        message_count += 1
                        logging.info(f'📨 Received message #{message_count}: {len(message)} bytes')
                        try:
                            parsed_message = json.loads(message)
                            # Log message type for debugging
                            msg_type = parsed_message.get('type', 'unknown')
                            logging.info(f'📨 Message type: {msg_type}')
                            if msg_type in ['conversation.item.input_audio_buffer.committed', 'conversation.item.added', 'response.content_part.added']:
                                logging.info(f'📨 Full message: {json.dumps(parsed_message, indent=2)}')
                            self.on_msg(parsed_message)
                        except json.JSONDecodeError as e:
                            logging.error(f'❌ Failed to parse message: {e}')
                            logging.error(f'Raw message: {message[:200]}...')
                except Exception as e:
                    # This is expected when no message is available
                    if "timed out" not in str(e).lower():
                        logging.error(f'❌ Error receiving message: {e}')
                    continue
            except WebSocketConnectionClosedException:
                logging.error('❌ WebSocket connection closed.')
                break
        
        logging.info(f'🔄 WebSocket loop ended. Total messages received: {message_count}')

    def send(self, data):
        """Enqueue the message to be sent."""
        self.send_queue.put(data)

    def kill(self):
        """Cleanly shut down the WebSocket and stop the loop."""
        logging.info('Shutting down WebSocket.')
        self._stop_event.set()

        # Close WebSocket
        if self.ws:
            try:
                self.ws.send_close()
                self.ws.close()
                logging.info('WebSocket connection closed.')
            except Exception as e:
                logging.error(f'Error closing WebSocket: {e}')

        # Ensure the loop thread is joined
        if self.loop_thread:
            self.loop_thread.join()
            logging.info('WebSocket loop thread terminated.')

class AudioIO:
    """Audio input/output handler."""
    
    def __init__(self, chunk_size=CHUNK_SIZE, rate=INPUT_SAMPLE_RATE, format=FORMAT, on_audio_callback=None):
        self.chunk_size = chunk_size
        self.rate = rate
        self.format = format
        self.audio_buffer = bytearray()
        self.mic_queue = queue.Queue()
        self.mic_on_at = 0
        self.mic_active = None
        self._stop_event = threading.Event()
        self.p = pyaudio.PyAudio()
        self.on_audio_callback = on_audio_callback

    def _mic_callback(self, in_data, frame_count, time_info, status):
        """Microphone callback that queues audio chunks."""
        if time.time() > self.mic_on_at:
            if not self.mic_active:
                logging.info('🎙️🟢 Mic active - you can speak now!')
                self.mic_active = True
            self.mic_queue.put(in_data)
        else:
            if self.mic_active:
                logging.info('🎙️🔴 Mic suppressed (waiting for AI to finish)')
                self.mic_active = False
        return (None, pyaudio.paContinue)

    def _spkr_callback(self, in_data, frame_count, time_info, status):
        """Speaker callback that plays audio."""
        bytes_needed = frame_count * 2
        current_buffer_size = len(self.audio_buffer)

        if current_buffer_size >= bytes_needed:
            audio_chunk = bytes(self.audio_buffer[:bytes_needed])
            self.audio_buffer = self.audio_buffer[bytes_needed:]
            self.mic_on_at = time.time() + REENGAGE_DELAY_MS / 1000
        else:
            audio_chunk = bytes(self.audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
            self.audio_buffer.clear()

        return (audio_chunk, pyaudio.paContinue)

    def start_streams(self):
        """Start microphone and speaker streams."""
        self.mic_stream = self.p.open(
            format=self.format,
            channels=1,
            rate=self.rate,
            input=True,
            stream_callback=self._mic_callback,
            frames_per_buffer=self.chunk_size
        )
        self.spkr_stream = self.p.open(
            format=self.format,
            channels=1,
            rate=self.rate,
            output=True,
            stream_callback=self._spkr_callback,
            frames_per_buffer=self.chunk_size
        )
        self.mic_stream.start_stream()
        self.spkr_stream.start_stream()

    def stop_streams(self):
        """Stop and close audio streams."""
        self.mic_stream.stop_stream()
        self.mic_stream.close()
        self.spkr_stream.stop_stream()
        self.spkr_stream.close()
        self.p.terminate()

    def process_mic_audio(self):
        """Process microphone audio and call back when new audio is ready."""
        while not self._stop_event.is_set():
            if not self.mic_queue.empty():
                mic_chunk = self.mic_queue.get()
                logging.info(f'🎤 Processing {len(mic_chunk)} bytes of audio data.')
                if self.on_audio_callback:
                    self.on_audio_callback(mic_chunk)
            else:
                time.sleep(0.05)

    def receive_audio(self, audio_chunk):
        """Appends audio data to the buffer for playback."""
        self.audio_buffer.extend(audio_chunk)

class RecordingManager:
    """Manages audio recording and transcript saving for conversations."""
    
    def __init__(self):
        self.recording_started = False
        self.audio_frames = []
        self.transcript_data = []
        self.session_id = str(uuid.uuid4())[:8]
        self.start_time = None
        self.model_id = "openai-realtime"
        self.model_identification = {
            "model_id": "gpt-4o-realtime-preview-2024-10-01",
            "model_name": "openai-realtime",
            "model_version": "1.0",
            "provider": "OpenAI",
            "model_type": "speech-to-speech",
            "configuration": {
                "voice": "alloy",
                "sample_rate_input": 24000,
                "sample_rate_output": 24000,
                "audio_format": "PCM16",
                "temperature": 0.8,
                "max_tokens": 4096  # Increased for better conversation handling
            },
            "system_info": {
                "python_version": "3.9.0",
                "os": "darwin 24.4.0",
                "region": "us-east-1"
            }
        }
        
        # Create output directories
        self.recordings_dir = Path("data/outputs/recordings")
        self.transcripts_dir = Path("data/outputs/transcripts")
        self.recordings_dir.mkdir(parents=True, exist_ok=True)
        self.transcripts_dir.mkdir(parents=True, exist_ok=True)
        
        # Audio recording setup
        self.audio_format = pyaudio.paInt16
        self.channels = 1
        self.sample_rate = 24000
        self.chunk_size = 1024
        self.audio = pyaudio.PyAudio()
        
        # Separate audio buffers for user and assistant
        self.user_audio_frames = []
        self.assistant_audio_frames = []
        
    def start_recording(self):
        """Start recording audio and transcript."""
        if self.recording_started:
            return
            
        self.recording_started = True
        self.start_time = datetime.datetime.now()
        self.user_audio_frames = []
        self.assistant_audio_frames = []
        self.transcript_data = []
        
        print(f"🎙️  Recording started - Session ID: {self.session_id}")
        
    def add_user_audio_frame(self, audio_data):
        """Add user audio frame to recording."""
        if self.recording_started and audio_data:
            self.user_audio_frames.append(audio_data)
    
    def add_assistant_audio_frame(self, audio_data):
        """Add assistant audio frame to recording."""
        if self.recording_started and audio_data:
            self.assistant_audio_frames.append(audio_data)
    
    def add_transcript_entry(self, speaker, text, timestamp=None):
        """Add transcript entry."""
        if self.recording_started:
            if timestamp is None:
                timestamp = datetime.datetime.now()
            
            self.transcript_data.append({
                "timestamp": timestamp.isoformat(),
                "speaker": speaker,
                "text": text
            })
    
    def stop_recording(self):
        """Stop recording and save files."""
        if not self.recording_started:
            return
            
        self.recording_started = False
        end_time = datetime.datetime.now()
        
        # Save audio recording
        audio_filename = self._save_audio_recording()
        
        # Save transcript
        transcript_filename = self._save_transcript()
        
        duration = end_time - self.start_time
        print(f"🎙️  Recording stopped - Duration: {duration}")
        print(f"📁 Files saved:")
        print(f"   Audio: {audio_filename}")
        print(f"   Transcript: {transcript_filename}")
        
    def _save_audio_recording(self):
        """Save audio recording to a single mixed WAV file."""
        user_frames = len(self.user_audio_frames) if self.user_audio_frames else 0
        assistant_frames = len(self.assistant_audio_frames) if self.assistant_audio_frames else 0
        
        if user_frames == 0 and assistant_frames == 0:
            print("⚠️  No audio frames to save")
            return None
        
        # Create a single mixed audio file using Config filename generation
        timestamp = int(time.time())
        mixed_filename = self.recordings_dir / Config.generate_filename(self.model_id, timestamp, "wav", "recording")
        
        try:
            # Combine all audio frames (user + assistant)
            all_audio_frames = []
            
            # Add user audio
            if self.user_audio_frames:
                all_audio_frames.extend(self.user_audio_frames)
            
            # Add assistant audio
            if self.assistant_audio_frames:
                all_audio_frames.extend(self.assistant_audio_frames)
            
            # Save the mixed audio file
            with wave.open(str(mixed_filename), 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(self.audio.get_sample_size(self.audio_format))
                wf.setframerate(24000)
                wf.writeframes(b''.join(all_audio_frames))
            
            total_frames = len(all_audio_frames)
            print(f"✅ Mixed audio saved: {total_frames} frames ({user_frames} user + {assistant_frames} assistant) at 24kHz")
            return str(mixed_filename)
            
        except Exception as e:
            print(f"❌ Error saving mixed audio: {e}")
            return None
    
    def _save_transcript(self):
        """Save transcript to JSON file."""
        if not self.transcript_data:
            return None
            
        timestamp = int(time.time())
        filename = self.transcripts_dir / Config.generate_filename(self.model_id, timestamp, "json", "transcript")
        
        transcript_summary = {
            "model_identification": self.model_identification,
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "end_time": datetime.datetime.now().isoformat(),
            "duration_seconds": (datetime.datetime.now() - self.start_time).total_seconds(),
            "total_entries": len(self.transcript_data),
            "transcript": self.transcript_data
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(transcript_summary, f, indent=2, ensure_ascii=False)
        
        return str(filename)
    
    def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'audio'):
            self.audio.terminate()

class RealtimeVoiceAgent:
    """Main voice agent using Socket and AudioIO classes."""
    
    def __init__(self):
        self.api_key = OPENAI_API_KEY
        self.ws_url = f'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview'
        self.model_id = "openai-realtime"  # Add model_id attribute
        
        # OpenAI Realtime API handles speech-to-text natively (like Bedrock in nova_sonic)
        
        # Initialize components
        self.socket = Socket(self.api_key, self.ws_url, on_msg=self.handle_message)
        self.audio_io = AudioIO(on_audio_callback=self.send_audio_to_socket)
        self.audio_thread = None
        
        # Load JD and Resume
        self.jd_text, self.resume_text = load_jd_and_resume()
        
        # Initialize recording and metrics
        self.recording_manager = RecordingManager()
        self.metrics_collector = MetricsCollector(model_id="openai-realtime")
        
        
        # Checkpoint system (like nova_sonic)
        self.metrics_save_counter = 0
        self.last_metrics_save = time.time()
        self.checkpoint_dir = None
        self.checkpoint_files = []
        
        # Interview state
        self.current_question = None
        self.questions_asked = []
        self.conversation_state = {
            "current_question_index": 0,
            "questions_asked": [],
            "user_responses": [],
            "last_user_response": None,
            "extracted_info": {
                "experience_years": None,
                "programming_languages": [],
                "ai_ml_experience": [],
                "generative_ai_experience": [],
                "cloud_platforms": [],
                "data_processing": [],
                "current_role": None,
                "projects_mentioned": [],
                "technologies_mentioned": []
            },
            "topics_covered": [],
            "adaptive_questions": []
        }
        
        # System message for interview
        self.system_message = self._generate_system_message()
        logging.info(f'📝 Generated system message: {self.system_message[:200]}...')
        
    def _generate_system_message(self):
        """Generate system message using ONLY the prompts from prompts.py file."""
        try:
            logging.info(f'📝 JD text length: {len(self.jd_text) if self.jd_text else 0}')
            logging.info(f'📝 Resume text length: {len(self.resume_text) if self.resume_text else 0}')
            
            # Always use prompts from prompts.py - no fallback
            role_type = self._extract_role_type()
            logging.info(f'📝 Extracted role type: {role_type}')
            
            # Use the existing prompt system properly
            l1_questions = QuestionGeneration.generate_l1_screening_questions(
                jd_text=self.jd_text or "No job description available",
                resume_text=self.resume_text or "No resume available", 
                role_type=role_type
            )
            logging.info(f'📝 Generated questions: {l1_questions[:200]}...')
            
            # Use the SAME instructions as Nova Sonic for consistency
            system_prompt = InterviewInstructions.get_l1_screening_instructions(
                role_type=role_type,
                questions=l1_questions
            )
            logging.info(f'📝 Generated system prompt: {system_prompt[:200]}...')
            
            # Record question alignment metrics using the comprehensive metrics system
            self.metrics_collector.record_question_alignment_metrics(
                questions=l1_questions,
                jd_text=self.jd_text or "",
                resume_text=self.resume_text or "",
                role_type=role_type
            )
            
            # Add tool definitions for knowledge retrieval and end_call
            system_prompt += self._get_tool_definitions()
            
            # Monitor system message length - CRITICAL: OpenAI Realtime API has strict limits
            estimated_tokens = len(system_prompt) // 4
            if estimated_tokens > 1000:  # Much stricter limit for Realtime API
                logging.warning(f"⚠️ System message too long: {len(system_prompt)} chars (~{estimated_tokens} tokens)")
                # Truncate to essential parts only
                system_prompt = f"""You are an expert L1 screening interviewer conducting a live speech-to-speech interview for a {role_type} position.

CRITICAL RULES:
1. Start with: "Hello! I'm your AI interviewer today. Thank you for joining us for this L1 screening interview for the {role_type} position. I'll be asking you some questions about your background and experience. Let's begin with our first question: Tell me about yourself and your background relevant to this role."
2. Ask ONE question at a time from this list:
{questions}
3. NEVER repeat questions
4. Acknowledge responses before moving to next question
5. Keep responses brief and natural for speech

Start the interview now."""
                logging.info(f"✅ Truncated system message to {len(system_prompt)} chars")
            
            logging.info(f"Using prompts from prompts.py for {role_type} role")
            return system_prompt
            
        except Exception as e:
            logging.error(f"Error generating system message: {e}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
            # Even on error, use a basic prompt from prompts.py instead of fallback
            try:
                basic_prompt = PromptTemplates.get_interview_prompt("general", "openai_realtime", 
                                                                  role_type="Software Engineer",
                                                                  retrieval_context="",
                                                                  jd_text=self.jd_text or "",
                                                                  resume_text=self.resume_text or "",
                                                                  questions="Tell me about yourself and your background.")
                logging.info("Using basic prompt from prompts.py as fallback")
                return basic_prompt
            except Exception as e2:
                logging.error(f"Even basic prompt failed: {e2}")
                # Last resort - use a simple prompt from prompts.py
                return "You are an expert interviewer conducting a live speech-to-speech interview. Ask questions about the candidate's background and experience. Start with: 'Hello! I'm your AI interviewer today. Tell me about yourself and your background.'"
    
    def _get_tool_definitions(self):
        """Get tool definitions for the model."""
        return """

## AVAILABLE TOOLS

You have access to the following tools:

1. **retrieve_knowledge(query)**: Retrieve information from the knowledge base
   - Use this to get specific information about the candidate's background
   - Example: retrieve_knowledge("candidate's Python experience")

2. **end_call(reason)**: End the interview gracefully
   - Use this when the interview is complete or candidate wants to end
   - Example: end_call("Interview completed successfully")

## TOOL USAGE RULES:
- Use retrieve_knowledge when you need specific information about the candidate
- Use end_call when the interview should end (after 5+ questions or candidate requests)
- Always provide a reason when ending the call
"""
    
    
    def _extract_role_type(self):
        """Extract role type from job description."""
        if not self.jd_text:
            return "Software Engineer"
        
        jd_lower = self.jd_text.lower()
        if any(keyword in jd_lower for keyword in ['senior', 'lead', 'principal']):
            return "Senior Software Engineer"
        elif any(keyword in jd_lower for keyword in ['junior', 'entry', 'graduate']):
            return "Junior Software Engineer"
        elif any(keyword in jd_lower for keyword in ['frontend', 'front-end', 'react', 'angular']):
            return "Frontend Developer"
        elif any(keyword in jd_lower for keyword in ['backend', 'back-end', 'api', 'server']):
            return "Backend Developer"
        elif any(keyword in jd_lower for keyword in ['fullstack', 'full-stack', 'full stack']):
            return "Full Stack Developer"
        else:
            return "Software Engineer"
    
    def _update_conversation_state(self, speaker, text):
        """Update conversation state."""
        if speaker == "Assistant":
            # Check if this is a question
            if "?" in text and any(keyword in text.lower() for keyword in ["tell me", "what", "how", "can you", "describe", "explain"]):
                self.conversation_state["questions_asked"].append(text)
                self.conversation_state["current_question_index"] += 1
                self.current_question = text
                self.questions_asked.append(text)
        elif speaker == "User":
            self.conversation_state["user_responses"].append(text)
            self.conversation_state["last_user_response"] = text
            
            # Parse response for key information
            self._parse_user_response(text)
    
    def _get_conversation_context(self):
        """Get current conversation context for the model (optimized for token efficiency)."""
        context = f"\n=== CONVERSATION STATE ===\n"
        context += f"Questions: {len(self.conversation_state['questions_asked'])}\n"
        context += f"Responses: {len(self.conversation_state['user_responses'])}\n"
        
        # Show recent user responses (last 2)
        if self.conversation_state['user_responses']:
            context += f"\nRECENT RESPONSES:\n"
            recent_responses = self.conversation_state['user_responses'][-2:]  # Last 2 responses
            for i, response in enumerate(recent_responses, 1):
                context += f"{i}. {response[:60]}...\n"
        
        if self.conversation_state['last_user_response']:
            context += f"\nLAST RESPONSE: {self.conversation_state['last_user_response'][:80]}...\n"
        
        # Add extracted information (only if available)
        extracted = self.conversation_state['extracted_info']
        if any(extracted.values()):
            context += f"\n=== EXTRACTED INFO ===\n"
            if extracted["experience_years"]:
                context += f"Experience: {extracted['experience_years']}\n"
            if extracted["programming_languages"]:
                context += f"Languages: {', '.join(extracted['programming_languages'])}\n"
            if extracted["ai_ml_experience"]:
                context += f"AI/ML: {', '.join(extracted['ai_ml_experience'])}\n"
            if extracted["generative_ai_experience"]:
                context += f"GenAI: {', '.join(extracted['generative_ai_experience'])}\n"
            if extracted["cloud_platforms"]:
                context += f"Cloud: {', '.join(extracted['cloud_platforms'])}\n"
            if extracted["current_role"]:
                context += f"Role: {extracted['current_role'][:50]}...\n"
        
        # Add specific skipping instructions based on extracted info
        context += f"\n=== SKIP RULES ===\n"
        if extracted.get("experience_years") or len(extracted.get("ai_ml_experience", [])) > 0:
            context += f"❌ NO experience questions - have: {extracted.get('experience_years', 'N/A')}\n"
        if len(extracted.get("programming_languages", [])) > 0:
            context += f"❌ NO language questions - have: {', '.join(extracted['programming_languages'])}\n"
        if len(extracted.get("ai_ml_experience", [])) > 0 or len(extracted.get("generative_ai_experience", [])) > 0:
            context += f"❌ NO AI/ML questions - have: {', '.join(extracted['ai_ml_experience'] + extracted['generative_ai_experience'])}\n"
        if extracted.get("current_role"):
            context += f"❌ NO role questions - have: {extracted.get('current_role')[:50]}...\n"
        
        context += f"\n=== CRITICAL RULES ===\n"
        context += f"1. Asked {len(self.conversation_state['questions_asked'])} questions\n"
        context += f"2. DO NOT ask about info already provided\n"
        context += f"3. Acknowledge what user actually said\n"
        context += f"4. Ask follow-ups based on their responses\n"
        context += f"5. If user says 'I don't know', acknowledge and move on\n"
        context += f"6. NEVER repeat questions about covered topics\n"
        context += f"7. If asked 5+ questions, consider ending gracefully\n"
        context += f"8. If user says 'thank you', 'that's all', end interview\n"
        
        # Limit context length to prevent token overflow (reduced from 1000 to 600)
        if len(context) > 600:  # Reduced from 1000 to 600 for better performance
            context = context[:600] + "... [Context truncated]"
        
        # Estimate token usage (rough: 1 token ≈ 4 characters)
        estimated_tokens = len(context) // 4
        if estimated_tokens > 500:  # Warn if context is getting large
            logging.warning(f"⚠️ Large context: {len(context)} chars (~{estimated_tokens} tokens)")
        
        return context
    
    def _update_conversation_state(self, speaker, text):
        """Update conversation state to track questions and responses."""
        if speaker == "Assistant":
            # Check if this is a question (improved detection)
            if "?" in text and any(keyword in text.lower() for keyword in [
                "tell me", "what", "how", "can you", "describe", "explain", 
                "walk me through", "share", "experience with", "comfortable with"
            ]):
                # Clean the question text
                clean_question = text.strip()
                if clean_question not in self.conversation_state["questions_asked"]:
                    self.conversation_state["questions_asked"].append(clean_question)
                    self.conversation_state["current_question_index"] += 1
                    logging.info(f"📝 Question {self.conversation_state['current_question_index']} recorded: {clean_question[:50]}...")
        elif speaker == "User":
            self.conversation_state["user_responses"].append(text)
            self.conversation_state["last_user_response"] = text
            
            # Check for frustration indicators
            frustration_indicators = [
                "why we are", "rebutting", "already discussed", "right track", 
                "stop this", "end the interview", "not comfortable", "frustrated"
            ]
            
            if any(indicator in text.lower() for indicator in frustration_indicators):
                self.conversation_state["user_frustration_level"] = self.conversation_state.get("user_frustration_level", 0) + 1
                logging.warning(f"⚠️ User frustration detected (level: {self.conversation_state['user_frustration_level']})")
            
            logging.info(f"📝 User response recorded: {text[:50]}...")
            
            # Parse response for key information
            self._parse_user_response(text)
        
        # Increment metrics counter and save checkpoint periodically
        self.metrics_save_counter += 1
        self._save_checkpoint()
    
    def _parse_user_response(self, text):
        """Parse user response to extract key information."""
        text_lower = text.lower()
        extracted = self.conversation_state["extracted_info"]
        
        # Accumulate text from recent responses for better parsing
        recent_responses = self.conversation_state.get("user_responses", [])
        if len(recent_responses) > 0:
            # Combine last few responses for better context
            combined_text = " ".join(recent_responses[-3:] + [text])
            combined_lower = combined_text.lower()
        else:
            combined_lower = text_lower
        
        # Extract experience years - improved regex patterns
        import re
        
        # Try multiple patterns for years
        years_patterns = [
            r'(\d+)\s*(?:to\s*(\d+))?\s*years?',  # "5 years", "5 to 6 years"
            r'(\d+)\s*year\s*experience',  # "5 year experience"
            r'experience.*?(\d+)\s*years?',  # "experience with 5 years"
            r'(\d+)\s*years?\s*in',  # "5 years in"
            r'(\d+)\s*years?\s*of',  # "5 years of"
            r'(\d+)\s*years?\s*experience',  # "5 years experience"
        ]
        
        for pattern in years_patterns:
            years_match = re.search(pattern, combined_lower)
            if years_match:
                if years_match.group(2):  # Range like "5 to 6 years"
                    extracted["experience_years"] = f"{years_match.group(1)}-{years_match.group(2)} years"
                else:  # Single number like "6 years"
                    extracted["experience_years"] = f"{years_match.group(1)} years"
                break
        
        # Extract programming languages
        languages = ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust', 'r', 'scala', 'kotlin', 'swift']
        for lang in languages:
            if lang in combined_lower:
                if lang not in extracted["programming_languages"]:
                    extracted["programming_languages"].append(lang)
        
        # Extract AI/ML technologies
        ai_tech = ['deep learning', 'neural networks', 'machine learning', 'ai', 'artificial intelligence', 'ml', 'tensorflow', 'pytorch', 'keras', 'scikit-learn']
        for tech in ai_tech:
            if tech in combined_lower:
                if tech not in extracted["ai_ml_experience"]:
                    extracted["ai_ml_experience"].append(tech)
        
        # Extract generative AI experience
        gen_ai = ['generative ai', 'llm', 'large language model', 'gpt', 'chatgpt', 'langchain', 'autochain', 'crewai', 'ai agents', 'conversational ai', 'building ai', 'ai agent']
        for tech in gen_ai:
            if tech in combined_lower:
                if tech not in extracted["generative_ai_experience"]:
                    extracted["generative_ai_experience"].append(tech)
        
        # Extract cloud platforms
        cloud = ['aws', 'azure', 'gcp', 'google cloud', 'amazon web services', 'microsoft azure', 'kubernetes', 'docker', 'deployment', 'production']
        for platform in cloud:
            if platform in combined_lower:
                if platform not in extracted["cloud_platforms"]:
                    extracted["cloud_platforms"].append(platform)
        
        # Extract current role information
        role_indicators = ['currently', 'current role', 'team lead', 'engineer', 'developer', 'architect', 'manager', 'senior', 'junior']
        for indicator in role_indicators:
            if indicator in combined_lower:
                # Extract the sentence containing the role from combined text
                sentences = combined_text.split('.')
                for sentence in sentences:
                    if indicator in sentence.lower():
                        extracted["current_role"] = sentence.strip()
                        break
        
        # Print extracted information for debugging
        if any(extracted.values()):
            logging.info(f"🔍 Extracted info: {extracted}")
    
    def _send_conversation_context_update(self):
        """Send updated conversation context to the model."""
        try:
            # Get current conversation context
            context_update = self._get_conversation_context()
            
            # Limit context length to prevent token overflow
            if len(context_update) > 600:  # Reduced for better performance
                context_update = context_update[:600] + "... [Context truncated]"
            
            # Send context update as a message to the model
            context_message = {
                'type': 'conversation.item.create',
                'item': {
                    'type': 'message',
                    'role': 'user',
                    'content': [
                        {
                            'type': 'input_text',
                            'text': f"CONTEXT UPDATE: {context_update}"
                        }
                    ]
                }
            }
            
            self.socket.send(context_message)
            
            # Estimate token usage (rough: 1 token ≈ 4 characters)
            estimated_tokens = len(context_update) // 4
            logging.info(f"📝 Sent conversation context update to model (length: {len(context_update)}, ~{estimated_tokens} tokens)")
            
        except Exception as e:
            logging.warning(f"⚠️ Error sending conversation context update: {e}")
    
    def _end_interview_gracefully(self, reason):
        """End the interview gracefully."""
        try:
            logging.info(f"🎯 Interview completed: {reason}")
            
            # Save final metrics and summary
            self._save_session_data()
            
            # Stop the agent
            self.stop()
            
            logging.info("✅ Interview ended successfully")
            
        except Exception as e:
            logging.error(f"❌ Error ending interview gracefully: {e}")
    
    def start(self):
        """Start WebSocket and audio processing."""
        self.socket.connect()
        
        # Start metrics collection and recording
        self.metrics_collector.start_session()
        self.recording_manager.start_recording()
        self._setup_checkpoint_directory()
        logging.info("📊 Started metrics collection and recording for interview session")

        # Note: session.update will be sent after session.created event

        # Send initial greeting
        self.socket.send({
            'type': 'conversation.item.create',
            'item': {
                'type': 'message',
                'role': 'user',
                'content': [
                    {
                        'type': 'input_text',
                        'text': 'Start the interview'
                    }
                ]
            }
        })
        
        self.socket.send({'type': 'response.create'})

        # Start processing microphone audio
        self.audio_thread = threading.Thread(target=self.audio_io.process_mic_audio)
        self.audio_thread.start()

        # Start audio streams (mic and speaker)
        self.audio_io.start_streams()

        # Print usage instructions
        print("\n" + "="*60)
        print("🎤 OpenAI Realtime Voice Agent Started!")
        print("="*60)
        print("📋 Instructions:")
        print("   • Wait for the AI to finish speaking (mic will be suppressed)")
        print("   • When you see '🎙️🟢 Mic active - you can speak now!' - start talking")
        print("   • Speak clearly and wait for the AI to respond")
        print("   • Press Ctrl+C to stop the interview")
        print("="*60)
        print("🤖 AI is starting the interview...")
        print("="*60)

    def send_audio_to_socket(self, mic_chunk):
        """Callback function to send audio data to the socket (like nova_sonic sends to Bedrock)."""
        # Record user audio for recording
        self.recording_manager.add_user_audio_frame(mic_chunk)
        
        # Send audio directly to OpenAI Realtime API
        # With server_vad, we don't need to manually commit - the server handles it
        encoded_chunk = base64.b64encode(mic_chunk).decode('utf-8')
        self.socket.send({'type': 'input_audio_buffer.append', 'audio': encoded_chunk})
    

    def handle_message(self, message):
        """Handle incoming WebSocket messages."""
        event_type = message.get('type')
        
        # Log all events for debugging user transcription issues
        if event_type not in ['response.audio.delta']:  # Skip audio deltas to reduce noise
            logging.info(f'📨 Received: {event_type}')
        
        # REMOVED: Timeout-based commits were causing "buffer too small" errors
        # Let the server-side VAD handle audio buffer commits automatically

        if event_type == 'session.created':
            logging.info('📋 Session created, configuring for ReAct interview agent')
            
            # Proper OpenAI Realtime API session configuration based on official samples
            # Session configuration based on working LangChain React Voice Agent
            session_config = {
                'type': 'session.update',
                'session': {
                    'instructions': self.system_message,
                    'input_audio_transcription': {
                        'model': 'whisper-1',
                    },
                    'tools': [
                        {
                            'type': 'function',
                            'name': 'retrieve_knowledge',
                            'description': 'Retrieve relevant information from the knowledge base about the candidate.',
                            'parameters': {
                                'type': 'object',
                                'properties': {
                                    'query': {
                                        'type': 'string',
                                        'description': 'The query to search for in the knowledge base'
                                    }
                                },
                                'required': ['query']
                            }
                        },
                        {
                            'type': 'function',
                            'name': 'end_interview',
                            'description': 'End the interview session gracefully',
                            'parameters': {
                                'type': 'object',
                                'properties': {
                                    'reason': {
                                        'type': 'string',
                                        'description': 'Reason for ending the interview'
                                    }
                                },
                                'required': ['reason']
                            }
                        }
                    ]
                }
            }
            
            try:
                self.socket.send(session_config)
                logging.info('✅ Session configuration sent successfully')
            except Exception as e:
                logging.error(f'❌ Failed to send session config: {e}')
            
            # Create initial response to start the conversation
            try:
                self.socket.send({'type': 'response.create'})
                logging.info('✅ Response creation sent successfully')
            except Exception as e:
                logging.error(f'❌ Failed to create response: {e}')

        elif event_type == 'response.audio.delta':
            audio_content = base64.b64decode(message['delta'])
            self.audio_io.receive_audio(audio_content)
            
            # Record assistant audio for recording
            self.recording_manager.add_assistant_audio_frame(audio_content)

        elif event_type == 'response.audio.done':
            logging.info('🤖 AI finished speaking.')
            
        elif event_type == 'response.audio_transcript.delta':
            # Handle AI speech transcript
            transcript_delta = message.get('delta', '')
            if transcript_delta:
                logging.info(f'🤖 AI transcript delta: {transcript_delta}')
                
        elif event_type == 'response.audio_transcript.done':
            # Handle completed AI speech transcript
            transcript = message.get('transcript', '')
            if transcript:
                logging.info(f'🤖 AI complete transcript: {transcript}')
                # Record assistant transcript
                self.recording_manager.add_transcript_entry("Assistant", transcript)
                # Update conversation state
                self._update_conversation_state("Assistant", transcript)
                # Record assistant turn metrics using comprehensive metrics system
                self.metrics_collector.record_assistant_turn(
                    response=transcript,
                    context_used="interview_context",
                    timestamp=time.time()
                )
                
                # Record enhanced speech metrics for comprehensive analysis
                self.metrics_collector.record_enhanced_speech_metrics(
                    audio_data=b"",  # No raw audio available
                    transcript=transcript
                )
                
                # Record enhanced single turn metrics
                self.metrics_collector.record_enhanced_single_turn_metrics(
                    intent_predicted="assistant_response",
                    response_appropriateness=0.8  # Default appropriateness
                )
                
                # Record conversation flow metrics
                self.metrics_collector.record_conversation_flow_metrics(
                    turn_type="assistant",
                    interruption_occurred=False,
                    context_maintained=True
                )
                
                # Record generation quality metrics
                self.metrics_collector.record_generation_quality(
                    response=transcript,
                    user_input="",  # Will be filled from conversation context
                    context="interview_context",
                    appropriateness_score=0.8
                )
                
                # Record response diversity metrics
                previous_responses = [entry.get('response', '') for entry in self.metrics_collector.interaction_metrics.get('conversation_flow', []) if entry.get('type') == 'assistant']
                self.metrics_collector.record_response_diversity(
                    current_response=transcript,
                    previous_responses=previous_responses
                )
            
        elif event_type == 'response.content_part.added':
            # Handle text content
            content = message.get('part', {})
            if content.get('type') == 'text':
                text_content = content.get('text', '')
                if text_content:
                    logging.info(f'🤖 Assistant: {text_content}')
                    
                    # Record assistant transcript
                    self.recording_manager.add_transcript_entry("Assistant", text_content)
                    
                    # Update conversation state
                    self._update_conversation_state("Assistant", text_content)
                    
                    # Record assistant turn metrics
                    self.metrics_collector.record_assistant_turn(
                        response=text_content,
                        context_used="interview_context",
                        timestamp=time.time()
                    )
                    
                    # Record enhanced metrics for comprehensive analysis
                    self.metrics_collector.record_enhanced_speech_metrics(
                        audio_data=b"",  # No raw audio available
                        transcript=text_content
                    )
                    
                    self.metrics_collector.record_enhanced_single_turn_metrics(
                        intent_predicted="assistant_response",
                        response_appropriateness=0.8  # Default appropriateness
                    )
        
        elif event_type == 'conversation.item.created':
            # Handle user speech transcription - EXACT same as nova_sonic handles USER role
            item = message.get('item', {})
            logging.info(f'🔍 DEBUG: Conversation item created: {json.dumps(item, indent=2)}')
            
            if item.get('role') == 'user' and item.get('type') == 'message':
                content = item.get('content', [])
                for content_item in content:
                    if content_item.get('type') == 'input_audio':
                        # Try both 'transcript' and 'text' fields
                        transcript = content_item.get('transcript', '') or content_item.get('text', '')
                        if transcript and len(transcript.strip()) > 0:
                            logging.info(f'👤 User: {transcript}')
                            
                            # Record user transcript - EXACT same as nova_sonic
                            self.recording_manager.add_transcript_entry("User", transcript)
                            
                            # Update conversation state - EXACT same as nova_sonic
                            self._update_conversation_state("User", transcript)
                            
                            # Don't send context updates - they cause feedback loops
                            
                            # Record user turn metrics using comprehensive metrics system
                            self.metrics_collector.record_user_turn(
                                audio_data=b"",  # We don't have raw audio here
                                transcript=transcript,
                                timestamp=time.time()
                            )
                            
                            # Record enhanced speech metrics for comprehensive analysis
                            self.metrics_collector.record_enhanced_speech_metrics(
                                audio_data=b"",  # No raw audio available
                                transcript=transcript
                            )
                            
                            # Record enhanced single turn metrics
                            self.metrics_collector.record_enhanced_single_turn_metrics(
                                intent_predicted="user_input",
                                asr_confidence=0.8  # Default confidence
                            )
                            
                            # Record conversation flow metrics
                            self.metrics_collector.record_conversation_flow_metrics(
                                turn_type="user",
                                interruption_occurred=False,
                                context_maintained=True
                            )
                            
                            # Record generation quality metrics
                            self.metrics_collector.record_generation_quality(
                                response=transcript,
                                user_input="",  # This is user input, not response
                                context="",
                                appropriateness_score=0.8
                            )
                            
                            logging.info(f'📊 Recorded user turn: "{transcript[:50]}..."')
                        else:
                            logging.info('📝 Empty transcript in conversation.item.created')
        
        elif event_type == 'conversation.item.input_audio_transcription.completed':
            # Handle completed audio transcription
            item = message.get('item', {})
            logging.info(f'🔍 DEBUG: Audio transcription completed: {json.dumps(item, indent=2)}')
            
            if item.get('role') == 'user':
                transcript = item.get('transcript', '')
                if transcript and len(transcript.strip()) > 0:
                    logging.info(f'👤 User (transcription completed): {transcript}')
                    
                    # Record user transcript
                    self.recording_manager.add_transcript_entry("User", transcript)
                    
                    # Update conversation state
                    self._update_conversation_state("User", transcript)
                    
                    # Record user turn metrics
                    self.metrics_collector.record_user_turn(
                        audio_data=b"",
                        transcript=transcript,
                        timestamp=time.time()
                    )
                    
                    # Record enhanced metrics
                    self.metrics_collector.record_enhanced_speech_metrics(
                        audio_data=b"",
                        transcript=transcript
                    )
                    
                    self.metrics_collector.record_enhanced_single_turn_metrics(
                        intent_predicted="user_input",
                        asr_confidence=0.8
                    )
                    
                    logging.info(f'📊 Recorded user turn (transcription completed): "{transcript[:50]}..."')
                else:
                    logging.info('📝 Empty transcript in transcription completed')
        
        elif event_type == 'input_audio_buffer.committed':
            # Handle committed audio buffer
            logging.info(f'🔍 DEBUG: Audio buffer committed: {json.dumps(message, indent=2)}')
        
        elif event_type == 'input_audio_buffer.speech_stopped':
            # Handle speech stopped - this might trigger transcription
            logging.info(f'🔍 DEBUG: Speech stopped: {json.dumps(message, indent=2)}')
        
        elif event_type == 'input_audio_buffer.speech_started':
            # Handle speech started
            logging.info(f'🔍 DEBUG: Speech started: {json.dumps(message, indent=2)}')
        
        elif event_type == 'conversation.item.input_audio_transcription.delta':
            # Handle transcription delta updates
            delta = message.get('delta', '')
            if delta:
                logging.info(f'👤 User transcript delta: {delta}')
        
        elif event_type == 'response.created':
            # Handle response creation
            logging.info(f'🔍 Response created: {json.dumps(message, indent=2)}')
        
        elif event_type == 'response.output_item.added':
            # Handle output item addition
            logging.info(f'🔍 Output item added: {json.dumps(message, indent=2)}')
        
        elif event_type == 'response.content_part.done':
            # Handle content part completion
            logging.info(f'🔍 Content part done: {json.dumps(message, indent=2)}')
        
        elif event_type == 'response.output_item.done':
            # Handle output item completion
            logging.info(f'🔍 Output item done: {json.dumps(message, indent=2)}')
        
        elif event_type == 'response.done':
            # Handle response completion
            logging.info(f'🔍 Response done: {json.dumps(message, indent=2)}')
        
        elif event_type == 'rate_limits.updated':
            # Handle rate limit updates
            logging.info(f'🔍 Rate limits updated: {json.dumps(message, indent=2)}')
                            
        elif event_type == 'response.function_call_arguments.done':
            # Handle tool calls
            self._handle_tool_call(message)
        elif event_type == 'response.function_call.done':
            # Handle completed function calls
            self._handle_function_call_done(message)
        
        
        elif event_type == 'error':
            # Handle error messages
            error_info = message.get('error', {})
            error_code = error_info.get('code', 'unknown')
            error_message = error_info.get('message', 'Unknown error')
            
            if error_code == 'input_audio_buffer_commit_empty':
                logging.warning(f'⚠️ Audio buffer commit failed: {error_message}')
                # Reset our buffer tracking to avoid repeated commits
                if hasattr(self, '_audio_buffer_size'):
                    self._audio_buffer_size = 0
            else:
                logging.error(f'❌ WebSocket error: {error_code} - {error_message}')
        
        else:
            # Log unknown message types for debugging
            if event_type not in ['response.audio.delta', 'response.audio_transcript.delta']:
                logging.info(f'🔍 Unknown message type: {event_type}')
                # Log all conversation.item events to see what we're missing
                if event_type.startswith('conversation.item'):
                    logging.info(f'🔍 Full conversation item message: {json.dumps(message, indent=2)}')
                elif event_type in ['conversation.item.created', 'response.output_item.added']:
                    logging.info(f'🔍 Full message: {json.dumps(message, indent=2)}')
            
    def _handle_tool_call(self, message):
        """Handle function/tool calls from the AI."""
        try:
            call_id = message.get('call_id')
            name = message.get('name')
            arguments = message.get('arguments', '{}')
            
            logging.info(f'Tool call: {name} with arguments: {arguments}')
            
            if name == 'retrieve_knowledge':
                # Enhanced RAG retrieval using LangChain knowledge base
                query = json.loads(arguments).get('query', '')
                logging.info(f'🔍 RAG Query: "{query}"')
                
                start_time = time.time()
                try:
                    # Use the existing LangChain knowledge base
                    result = knowledge_retrieval(query)
                    retrieval_time = time.time() - start_time
                    
                    # Enhanced RAG metrics recording using the comprehensive metrics system
                    if result.get('status') == 'success':
                        contexts = result.get('contexts', [])
                        logging.info(f'📊 RAG Success: Retrieved {len(contexts)} contexts in {retrieval_time:.2f}s')
                        
                        # Record comprehensive RAG metrics using the existing metrics system
                        self.metrics_collector.record_rag_retrieval_metrics(
                            query=query,
                            retrieved_docs=contexts,
                            retrieval_time=retrieval_time
                        )
                        
                        # Record additional RAG metrics
                        self.metrics_collector.record_rag_context_utilization_metrics(
                            retrieved_context=str(contexts),
                            response="",  # Will be filled when response is generated
                            context_used=str(contexts),
                            context_window_size=4096
                        )
                        
                        # Record enhanced single turn metrics
                        self.metrics_collector.record_enhanced_single_turn_metrics(
                            intent_predicted="rag_retrieval",
                            response_appropriateness=0.9 if len(contexts) > 0 else 0.3
                        )
                        
                    else:
                        logging.warning(f'⚠️ RAG failed: {result.get("error", "Unknown error")}')
                        result = {
                            'status': 'error',
                            'error': result.get('error', 'Knowledge retrieval failed'),
                            'contexts': []
                        }
                        
                        # Record RAG error metrics
                        self.metrics_collector.record_rag_error_metrics(
                            retrieval_success=False,
                            generation_success=False,
                            fallback_used=True
                        )
                        
                except Exception as e:
                    logging.error(f'❌ RAG retrieval error: {e}')
                    result = {
                        'status': 'error',
                        'error': str(e),
                        'contexts': []
                    }
                    
                    # Record RAG error metrics
                    self.metrics_collector.record_rag_error_metrics(
                        retrieval_success=False,
                        generation_success=False,
                        fallback_used=True
                    )
                
                # Send tool result back with enhanced format
                tool_result = {
                    'type': 'conversation.item.create',
                    'item': {
                        'type': 'function_call_output',
                        'call_id': call_id,
                        'output': result
                    }
                }
                
                try:
                    self.socket.send(tool_result)
                    logging.info('✅ Tool result sent successfully')
                except Exception as e:
                    logging.error(f'❌ Failed to send tool result: {e}')
                
            elif name == 'end_interview':
                # Enhanced interview ending
                reason = json.loads(arguments).get('reason', 'Interview completed')
                summary = json.loads(arguments).get('summary', 'Interview session ended')
                
                logging.info(f'🏁 Ending interview: {reason}')
                logging.info(f'📝 Summary: {summary}')
                
                # Record final metrics using the comprehensive metrics system
                self.metrics_collector.record_enhanced_single_turn_metrics(
                    intent_predicted="interview_end",
                    response_appropriateness=1.0
                )
                
                # Record RAG user experience metrics
                self.metrics_collector.record_rag_user_experience_metrics(
                    response_helpfulness=0.8,
                    information_quality=0.8,
                    answer_completeness=0.8,
                    user_satisfaction=0.8,
                    trust_score=0.8
                )
                
                # End the interview gracefully
                self._end_interview_gracefully(reason)
                
                # Send tool result
                tool_result = {
                    'type': 'conversation.item.create',
                    'item': {
                        'type': 'function_call_output',
                        'call_id': call_id,
                        'output': {
                            'status': 'success',
                            'message': f"Interview ended: {reason}",
                            'summary': summary
                        }
                    }
                }
                
                try:
                    self.socket.send(tool_result)
                    logging.info('✅ Interview end result sent successfully')
                except Exception as e:
                    logging.error(f'❌ Failed to send interview end result: {e}')
                
        except Exception as e:
            logging.error(f'Error handling tool call: {e}')
    
    def _handle_function_call_done(self, message):
        """Handle completed function calls."""
        try:
            call_id = message.get('call_id')
            name = message.get('name')
            
            logging.info(f'Function call completed: {name} (call_id: {call_id})')
            
        except Exception as e:
            logging.error(f'Error handling function call done: {e}')
    
    def _setup_checkpoint_directory(self):
        """Setup session-specific checkpoint directory."""
        try:
            # Get session ID from recording manager
            session_id = getattr(self.recording_manager, 'session_id', f'session_{int(time.time())}')
            
            # Create checkpoint directory structure
            self.checkpoint_dir = f"data/outputs/evaluations/.checkpoints/{session_id}"
            os.makedirs(self.checkpoint_dir, exist_ok=True)
            
            logging.info(f"📁 Checkpoint directory created: {self.checkpoint_dir}")
            
        except Exception as e:
            logging.error(f"⚠️ Error setting up checkpoint directory: {e}")
            # Fallback to a simple directory
            self.checkpoint_dir = f"data/outputs/evaluations/.checkpoints/fallback_{int(time.time())}"
            os.makedirs(self.checkpoint_dir, exist_ok=True)
    
    def _save_checkpoint(self):
        """Save a checkpoint with current metrics and conversation state."""
        try:
            current_time = time.time()
            
            # Save checkpoint every 30 seconds or every 5 turns
            if (current_time - self.last_metrics_save > 30 or 
                self.metrics_save_counter % 5 == 0):
                
                checkpoint_metrics = {
                    "checkpoint": True,
                    "turn_count": self.metrics_save_counter,
                    "total_turns": self.metrics_collector.interaction_metrics["total_turns"],
                    "user_turns": self.metrics_collector.interaction_metrics["user_turns"],
                    "assistant_turns": self.metrics_collector.interaction_metrics["assistant_turns"],
                    "timestamp": current_time,
                    "conversation_state": self.conversation_state,
                    "questions_asked": len(self.conversation_state.get("questions_asked", [])),
                    "user_responses": len(self.conversation_state.get("user_responses", []))
                }
                
                # Convert numpy types before saving
                checkpoint_metrics = self._convert_numpy_types(checkpoint_metrics)
                
                # Save to session-specific checkpoint directory
                checkpoint_file = os.path.join(self.checkpoint_dir, f"checkpoint_{int(current_time)}.json")
                
                with open(checkpoint_file, 'w') as f:
                    json.dump(checkpoint_metrics, f, indent=2)
                
                # Track checkpoint file for cleanup
                self.checkpoint_files.append(checkpoint_file)
                
                logging.info(f"💾 Checkpoint saved: {checkpoint_file}")
                self.last_metrics_save = current_time
                
        except Exception as e:
            logging.error(f"⚠️ Error saving checkpoint: {e}")
    
    def _save_final_checkpoint(self):
        """Save a final checkpoint before cleanup."""
        try:
            if not self.checkpoint_dir:
                return
            
            final_checkpoint = {
                "final_checkpoint": True,
                "session_completed": True,
                "total_turns": self.metrics_collector.interaction_metrics["total_turns"],
                "user_turns": self.metrics_collector.interaction_metrics["user_turns"],
                "assistant_turns": self.metrics_collector.interaction_metrics["assistant_turns"],
                "timestamp": time.time(),
                "conversation_state": self.conversation_state,
                "questions_asked": len(self.conversation_state.get("questions_asked", [])),
                "user_responses": len(self.conversation_state.get("user_responses", []))
            }
            
            # Convert numpy types
            final_checkpoint = self._convert_numpy_types(final_checkpoint)
            
            # Save final checkpoint
            final_file = os.path.join(self.checkpoint_dir, "final_checkpoint.json")
            with open(final_file, 'w') as f:
                json.dump(final_checkpoint, f, indent=2)
            
            logging.info(f"💾 Final checkpoint saved: {final_file}")
            
        except Exception as e:
            logging.error(f"⚠️ Error saving final checkpoint: {e}")
    
    def _cleanup_checkpoints(self):
        """Clean up checkpoint files after interview completion."""
        try:
            if not self.checkpoint_dir or not os.path.exists(self.checkpoint_dir):
                return
            
            # Count files before cleanup
            checkpoint_count = len(self.checkpoint_files)
            
            # Remove all checkpoint files
            for checkpoint_file in self.checkpoint_files:
                try:
                    if os.path.exists(checkpoint_file):
                        os.remove(checkpoint_file)
                except Exception as e:
                    logging.error(f"⚠️ Error removing checkpoint {checkpoint_file}: {e}")
            
            # Try to remove the checkpoint directory if it's empty
            try:
                if os.path.exists(self.checkpoint_dir):
                    os.rmdir(self.checkpoint_dir)
                    logging.info(f"🧹 Cleaned up checkpoint directory: {self.checkpoint_dir}")
            except OSError:
                # Directory not empty, that's okay
                logging.info(f"📁 Checkpoint directory kept (not empty): {self.checkpoint_dir}")
            
            if checkpoint_count > 0:
                logging.info(f"🧹 Cleaned up {checkpoint_count} checkpoint files")
            
        except Exception as e:
            logging.error(f"⚠️ Error during checkpoint cleanup: {e}")
    
    def _save_session_data(self):
        """Save session data including recordings and metrics."""
        try:
            # Save recording
            self.recording_manager.stop_recording()
            
            # Get comprehensive metrics before ending session (like nova_sonic)
            comprehensive_metrics = self.metrics_collector.get_comprehensive_metrics()
            
            # End metrics collection
            self.metrics_collector.end_session()
            
            # Save structured metrics (like nova_sonic)
            self._save_structured_metrics()
            
            # Save interview summary
            self._save_interview_summary(comprehensive_metrics)
            
            # Save final checkpoint before cleanup
            self._save_final_checkpoint()
            
            # Clean up checkpoint files
            self._cleanup_checkpoints()
            
        except Exception as e:
            logging.error(f'Error saving session data: {e}')
    
    def _save_structured_metrics(self):
        """Save structured metrics with all raw data (like nova_sonic)."""
        try:
            timestamp = int(time.time())
            
            # Get all raw metrics data
            structured_metrics = self.metrics_collector._prepare_comprehensive_export_data()
            
            # Add model identification to metrics
            metrics_with_model = {
                "model_identification": self.metrics_collector.model_identification,
                "metrics_data": structured_metrics
            }
            
            # Convert numpy types for JSON serialization
            metrics_with_model = self._convert_numpy_types(metrics_with_model)
            
            # Save to metrics directory with consistent naming
            metrics_file = f"data/outputs/evaluations/metrics/{Config.generate_filename(self.model_id, timestamp, 'json', 'metrics')}"
            os.makedirs(os.path.dirname(metrics_file), exist_ok=True)
            
            with open(metrics_file, 'w') as f:
                json.dump(metrics_with_model, f, indent=2)
            
            logging.info(f"📊 Structured metrics saved: {metrics_file}")
            
        except Exception as e:
            logging.error(f"⚠️ Error saving structured metrics: {e}")
    
    def _save_interview_summary(self, comprehensive_metrics):
        """Save interview summary with consistent naming (like nova_sonic)."""
        try:
            timestamp = int(time.time())
            
            # Create interview summary
            interview_summary = {
                "model_identification": self.metrics_collector.model_identification,
                
                "interview_info": {
                    "session_id": self.recording_manager.session_id,
                    "timestamp": timestamp,
                    "date": datetime.datetime.now().isoformat(),
                    "role_type": self._extract_role_type(),
                    "model_type": "openai_realtime"
                },
                
                "interview_metrics": {
                    "total_questions_asked": len(self.conversation_state.get("questions_asked", [])),
                    "total_user_responses": len(self.conversation_state.get("user_responses", [])),
                    "session_duration": (datetime.datetime.now() - self.recording_manager.start_time).total_seconds() if self.recording_manager.start_time else 0
                },
                
                "conversation_summary": {
                    "questions_asked": self.conversation_state.get("questions_asked", []),
                    "user_responses": self.conversation_state.get("user_responses", []),
                    "extracted_info": self.conversation_state.get("extracted_info", {})
                },
                
                "files_generated": {
                    "audio_recording": f"data/outputs/recordings/{Config.generate_filename(self.model_id, timestamp, 'wav', 'recording')}",
                    "transcript": f"data/outputs/transcripts/{Config.generate_filename(self.model_id, timestamp, 'json', 'transcript')}",
                    "metrics": f"data/outputs/evaluations/metrics/{Config.generate_filename(self.model_id, timestamp, 'json', 'metrics')}",
                    "summary": f"data/outputs/evaluations/summaries/{Config.generate_filename(self.model_id, timestamp, 'json', 'summary')}"
                }
            }
            
            # Save summary with consistent naming
            summary_file = f"data/outputs/evaluations/summaries/{Config.generate_filename(self.model_id, timestamp, 'json', 'summary')}"
            os.makedirs(os.path.dirname(summary_file), exist_ok=True)
            
            with open(summary_file, 'w') as f:
                json.dump(interview_summary, f, indent=2)
            
            logging.info(f"✅ Interview summary saved: {summary_file}")
            
        except Exception as e:
            logging.error(f'Error saving interview summary: {e}')
    
    def _convert_numpy_types(self, obj):
        """Convert numpy types to Python native types for JSON serialization."""
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        else:
            return obj

    def stop(self):
        """Stop all processes cleanly."""
        logging.info('Shutting down Realtime session.')
        
        # Save session data FIRST before stopping anything
        try:
            self._save_session_data()
            logging.info('✅ Session data saved successfully')
        except Exception as e:
            logging.error(f'❌ Error saving session data: {e}')

        # Signal threads to stop
        self.audio_io._stop_event.set()
        self.socket.kill()

        # Stop audio streams
        self.audio_io.stop_streams()

        # Join threads to ensure they exit cleanly
        if self.audio_thread:
            self.audio_thread.join()
            logging.info('Audio processing thread terminated.')

def main():
    """Main function using improved classes."""
    api_key = OPENAI_API_KEY
    ws_url = f'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview'

    if not api_key:
        logging.error('OPENAI_API_KEY not found in environment variables!')
        return

    realtime = RealtimeVoiceAgent()
    quitFlag = False

    def signal_handler(sig, frame):
        """Handle Ctrl+C and initiate graceful shutdown."""
        logging.info('Received Ctrl+C! Initiating shutdown...')
        realtime.stop()
        nonlocal quitFlag
        quitFlag = True

    signal.signal(signal.SIGINT, signal_handler)

    # Start a thread to monitor for Enter key press
    import threading
    def monitor_enter_key():
        """Monitor for Enter key press to stop the agent."""
        try:
            input()  # Wait for Enter key
            logging.info('Enter key pressed! Initiating shutdown...')
            realtime.stop()
            nonlocal quitFlag
            quitFlag = True
        except:
            pass  # Ignore errors when shutting down
    
    enter_thread = threading.Thread(target=monitor_enter_key, daemon=True)
    enter_thread.start()

    try:
        realtime.start()
        print("🎤 Voice agent started! Press Enter or Ctrl+C to stop.")
        
        while not quitFlag:
            time.sleep(0.1)

    except Exception as e:
        logging.error(f'Error in main loop: {e}')
        realtime.stop()

    finally:
        logging.info('Exiting main.')
        realtime.stop()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Failed to start application: {e}")