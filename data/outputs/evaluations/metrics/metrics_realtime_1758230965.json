{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech", "configuration": {"voice_id": "alloy", "sample_rate_input": 16000, "sample_rate_output": 24000, "audio_format": "PCM", "temperature": 0.7, "max_tokens": 1024}, "system_info": {"python_version": "3.9.0", "os": "darwin 24.4.0", "region": "us-east-1"}}, "metrics_data": {"session_info": {"start_time": **********.4398458, "end_time": **********.161484, "duration": 158.72163820266724, "total_turns": 3, "user_turns": 3, "assistant_turns": 0}, "speech_quality_metrics": {"wer_scores": [], "cer_scores": [], "per_scores": [], "pesq_scores": [], "stoi_scores": [], "estoi_scores": [], "mcd_scores": [], "speaker_embedding_similarity": [], "eer_scores": [], "duration_rhythm_deviation": [], "energy_contours": []}, "latency_metrics": {"asr_latency": [1.2903975248336792, 6.559200239181519, 0.27835171222686766, 0.8779898643493652, 0.9592914819717406], "tts_latency": [1.7205300331115723, 8.745600318908691, 0.37113561630249026, 1.1706531524658204, 1.2790553092956545], "turn_latency": [4.301325082778931, 21.86400079727173, 0.9278390407562256, 2.926632881164551, 3.1976382732391357], "barge_in_response_time": [], "first_token_latency": [1.2903975248336792, 6.559200239181519, 0.27835171222686766, 0.8779898643493652, 0.9592914819717406], "end_to_end_latency": [4.301325082778931, 21.86400079727173, 0.9278390407562256, 2.926632881164551, 3.1976382732391357]}, "single_turn_metrics": {"intent_accuracy": [], "slot_f1_scores": [], "response_appropriateness_score": [], "asr_confidence_scores": [], "usr_metric": []}, "multi_turn_metrics": {"task_success_rate": [], "conversation_length": [], "repetition_rate": []}, "no_ground_truth_metrics": {"toxicity_detection": [], "interruption_drop_offs": [], "user_satisfaction_proxy": []}, "rag_evaluation_metrics": {"retrieval_quality": {"retrieval_precision": [1.0], "retrieval_recall": [0.2777777777777778], "retrieval_f1_score": [0.4347826086956522], "retrieval_relevance_scores": [1.9266401529312134, 1.9271565675735474, 1.9271565675735474], "retrieval_diversity": [0.0], "retrieval_coverage": [1.0], "retrieval_latency": [0.0], "retrieval_rank_accuracy": []}, "context_utilization": {"context_utilization_rate": [], "context_relevance_score": [], "context_coherence": [], "context_integration_quality": [], "context_window_efficiency": [], "context_overflow_rate": []}, "context_awareness": {"context_awareness_score": [], "conversation_continuity_score": [], "response_relevance_score": [], "topic_coherence_score": [], "memory_retention_score": [], "adaptive_response_score": []}, "knowledge_base_quality": {"kb_coverage_score": [], "kb_freshness_score": [], "kb_consistency_score": [], "kb_completeness_score": [], "kb_accuracy_score": []}, "rag_generation_quality": {"rag_response_relevance": [], "rag_factual_accuracy": [], "rag_coherence_score": [], "rag_consistency_score": [], "rag_creativity_score": [], "rag_adaptation_quality": []}, "rag_efficiency": {"rag_total_latency": [], "rag_retrieval_efficiency": [], "rag_generation_efficiency": [], "rag_token_efficiency": [], "rag_cost_efficiency": [], "rag_throughput": []}, "rag_error_analysis": {"rag_hallucination_rate": [], "rag_context_misuse": [], "rag_retrieval_failures": [], "rag_generation_failures": [], "rag_fallback_usage": []}, "rag_user_experience": {"rag_response_helpfulness": [], "rag_information_quality": [], "rag_answer_completeness": [], "rag_user_satisfaction": [], "rag_trust_score": []}, "rag_conversation_flow": {"rag_context_continuity": [], "rag_topic_coherence": [], "rag_question_relevance": [], "rag_follow_up_quality": [], "rag_conversation_depth": []}, "rag_model_performance": {"rag_embedding_quality": [], "rag_similarity_accuracy": [], "rag_ranking_quality": [], "rag_reranking_effectiveness": [], "rag_fusion_quality": []}, "question_alignment_metrics": {"question_jd_alignment": [0.7124252915382385, 0.2799200713634491, 0.19182704389095306, 0.03189761936664581, 0.23472923040390015, 0.8432983756065369, 0.25899335741996765, 0.10792477428913116, 0.11069104075431824, 0.08977054804563522, 0.1664440780878067, 0.3635143041610718], "question_resume_alignment": [0.434613972902298, 0.23406749963760376, 0.26225176453590393, 0.04388953745365143, 0.3076489269733429, 0.27897658944129944, 0.11567705869674683, 0.007767674513161182, 0.09156352281570435, 0.09054194390773773, 0.28562361001968384, 0.3041572868824005], "question_technical_relevance": [1.0, 1.0, 0.5, 0.5, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 0.5, 0.5], "question_experience_relevance": [0.8, 0.8, 0.8, 0.5, 0.8, 0.8, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5], "question_role_specificity": [1.0, 0.8, 0.5, 0.5, 0.5, 1.0, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5], "question_difficulty_appropriateness": [1.0, 0.5, 0.5, 0.5, 1.0, 0.8, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5]}}, "conversation_flow": [{"turn_number": 1, "type": "user", "timestamp": 1758230877.065391, "turn_latency": 70.6255452632904, "transcript": "Yeah, I used a large language model for building a conversational AI agent and few of search-based rag-engine systems.", "audio_metrics": {"snr": 0, "audio_level": 0, "dynamic_range": 0, "sample_count": 0}, "speech_metrics": {"word_count": 19, "char_count": 118, "speech_rate": 1.6101694915254237, "vocabulary_diversity": 0.9473684210526315, "avg_sentence_length": 9.5, "estimated_duration": 11.8}}, {"turn_number": 2, "type": "user", "timestamp": 1758230897.44325, "turn_latency": 20.377858877182007, "transcript": "क्योंकि तुम्हारी वोईस ब्रेकिंग है?", "audio_metrics": {"snr": 0, "audio_level": 0, "dynamic_range": 0, "sample_count": 0}, "speech_metrics": {"word_count": 5, "char_count": 34, "speech_rate": 1.4705882352941178, "vocabulary_diversity": 1.0, "avg_sentence_length": 5.0, "estimated_duration": 3.4}}, {"turn_number": 3, "type": "user", "timestamp": 1758230943.315991, "turn_latency": 45.87274098396301, "transcript": "We use the event-driven architecture, it's all just simply a task for interviewing both candidates and generate a question and the simple aspects for them.", "audio_metrics": {"snr": 0, "audio_level": 0, "dynamic_range": 0, "sample_count": 0}, "speech_metrics": {"word_count": 25, "char_count": 155, "speech_rate": 1.6129032258064515, "vocabulary_diversity": 0.84, "avg_sentence_length": 12.5, "estimated_duration": 15.5}}], "summary_statistics": {"pesq_avg": 0, "pesq_min": 0, "pesq_max": 0, "stoi_avg": 0, "stoi_min": 0, "stoi_max": 0, "asr_latency_avg": 1.9930461645126343, "asr_latency_p95": 6.559200239181519, "tts_latency_avg": 2.6573948860168457, "tts_latency_p95": 8.745600318908691, "asr_confidence_avg": 0, "rag_response_relevance_avg": 0, "rag_response_relevance_min": 0, "rag_response_relevance_max": 0, "rag_factual_accuracy_avg": 0, "rag_factual_accuracy_min": 0, "rag_factual_accuracy_max": 0, "rag_coherence_avg": 0, "rag_coherence_min": 0, "rag_coherence_max": 0, "rag_context_utilization_avg": 0, "rag_context_utilization_min": 0, "rag_context_utilization_max": 0, "rag_hallucination_rate_avg": 0, "rag_hallucination_rate_max": 0, "rag_retrieval_failure_rate": 0, "rag_generation_failure_rate": 0}}}