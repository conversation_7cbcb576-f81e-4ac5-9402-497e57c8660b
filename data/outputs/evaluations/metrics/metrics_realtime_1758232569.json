{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech", "configuration": {"voice_id": "alloy", "sample_rate_input": 16000, "sample_rate_output": 24000, "audio_format": "PCM", "temperature": 0.7, "max_tokens": 1024}, "system_info": {"python_version": "3.9.0", "os": "darwin 24.4.0", "region": "us-east-1"}}, "metrics_data": {"session_info": {"start_time": **********.827657, "end_time": **********.198456, "duration": 68.37079906463623, "total_turns": 1, "user_turns": 1, "assistant_turns": 0}, "speech_quality_metrics": {"wer_scores": [], "cer_scores": [], "per_scores": [], "pesq_scores": [], "stoi_scores": [], "estoi_scores": [], "mcd_scores": [], "speaker_embedding_similarity": [], "eer_scores": [], "duration_rhythm_deviation": [], "energy_contours": []}, "latency_metrics": {"asr_latency": [1.5029889106750487], "tts_latency": [2.0039852142333987], "turn_latency": [5.009963035583496], "barge_in_response_time": [], "first_token_latency": [1.5029889106750487], "end_to_end_latency": [5.009963035583496]}, "single_turn_metrics": {"intent_accuracy": [], "slot_f1_scores": [], "response_appropriateness_score": [], "asr_confidence_scores": [], "usr_metric": []}, "multi_turn_metrics": {"task_success_rate": [], "conversation_length": [], "repetition_rate": []}, "no_ground_truth_metrics": {"toxicity_detection": [], "interruption_drop_offs": [], "user_satisfaction_proxy": []}, "rag_evaluation_metrics": {"retrieval_quality": {"retrieval_precision": [], "retrieval_recall": [], "retrieval_f1_score": [], "retrieval_relevance_scores": [], "retrieval_diversity": [], "retrieval_coverage": [], "retrieval_latency": [], "retrieval_rank_accuracy": []}, "context_utilization": {"context_utilization_rate": [], "context_relevance_score": [], "context_coherence": [], "context_integration_quality": [], "context_window_efficiency": [], "context_overflow_rate": []}, "context_awareness": {"context_awareness_score": [], "conversation_continuity_score": [], "response_relevance_score": [], "topic_coherence_score": [], "memory_retention_score": [], "adaptive_response_score": []}, "knowledge_base_quality": {"kb_coverage_score": [], "kb_freshness_score": [], "kb_consistency_score": [], "kb_completeness_score": [], "kb_accuracy_score": []}, "rag_generation_quality": {"rag_response_relevance": [], "rag_factual_accuracy": [], "rag_coherence_score": [], "rag_consistency_score": [], "rag_creativity_score": [], "rag_adaptation_quality": []}, "rag_efficiency": {"rag_total_latency": [], "rag_retrieval_efficiency": [], "rag_generation_efficiency": [], "rag_token_efficiency": [], "rag_cost_efficiency": [], "rag_throughput": []}, "rag_error_analysis": {"rag_hallucination_rate": [], "rag_context_misuse": [], "rag_retrieval_failures": [], "rag_generation_failures": [], "rag_fallback_usage": []}, "rag_user_experience": {"rag_response_helpfulness": [], "rag_information_quality": [], "rag_answer_completeness": [], "rag_user_satisfaction": [], "rag_trust_score": []}, "rag_conversation_flow": {"rag_context_continuity": [], "rag_topic_coherence": [], "rag_question_relevance": [], "rag_follow_up_quality": [], "rag_conversation_depth": []}, "rag_model_performance": {"rag_embedding_quality": [], "rag_similarity_accuracy": [], "rag_ranking_quality": [], "rag_reranking_effectiveness": [], "rag_fusion_quality": []}, "question_alignment_metrics": {"question_jd_alignment": [0.7124252915382385, 0.2799200713634491, 0.19182704389095306, 0.145681694149971, 0.23472923040390015, 0.8432983756065369, 0.25899335741996765, 0.10792477428913116, 0.11069104075431824, 0.08977054804563522, 0.1664440780878067, 0.3635143041610718], "question_resume_alignment": [0.434613972902298, 0.23406749963760376, 0.26225176453590393, 0.2033916711807251, 0.3076489269733429, 0.27897658944129944, 0.11567705869674683, 0.007767674513161182, 0.09156352281570435, 0.09054194390773773, 0.28562361001968384, 0.3041572868824005], "question_technical_relevance": [1.0, 1.0, 0.5, 0.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5], "question_experience_relevance": [0.8, 0.8, 0.8, 0.5, 1.0, 0.8, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5], "question_role_specificity": [1.0, 0.8, 0.5, 0.5, 0.5, 1.0, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5], "question_difficulty_appropriateness": [1.0, 0.5, 0.5, 0.5, 1.0, 1.0, 0.9, 1.0, 1.0, 1.0, 1.0, 0.5]}}, "conversation_flow": [{"turn_number": 1, "type": "user", "timestamp": 1758232565.5705159, "turn_latency": 64.74285888671875, "transcript": "Yeah, I worked on a conversational AI system to", "audio_metrics": {"snr": 0, "audio_level": 0, "dynamic_range": 0, "sample_count": 0}, "speech_metrics": {"word_count": 9, "char_count": 47, "speech_rate": 1.9148936170212765, "vocabulary_diversity": 1.0, "avg_sentence_length": 9.0, "estimated_duration": 4.7}}], "summary_statistics": {"pesq_avg": 0, "pesq_min": 0, "pesq_max": 0, "stoi_avg": 0, "stoi_min": 0, "stoi_max": 0, "asr_latency_avg": 1.5029889106750487, "asr_latency_p95": 1.5029889106750487, "tts_latency_avg": 2.0039852142333987, "tts_latency_p95": 2.0039852142333987, "asr_confidence_avg": 0, "rag_response_relevance_avg": 0, "rag_response_relevance_min": 0, "rag_response_relevance_max": 0, "rag_factual_accuracy_avg": 0, "rag_factual_accuracy_min": 0, "rag_factual_accuracy_max": 0, "rag_coherence_avg": 0, "rag_coherence_min": 0, "rag_coherence_max": 0, "rag_context_utilization_avg": 0, "rag_context_utilization_min": 0, "rag_context_utilization_max": 0, "rag_hallucination_rate_avg": 0, "rag_hallucination_rate_max": 0, "rag_retrieval_failure_rate": 0, "rag_generation_failure_rate": 0}}}