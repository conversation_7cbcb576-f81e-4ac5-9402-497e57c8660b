{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech", "configuration": {"voice_id": "alloy", "sample_rate_input": 16000, "sample_rate_output": 24000, "audio_format": "PCM", "temperature": 0.7, "max_tokens": 1024}, "system_info": {"python_version": "3.9.0", "os": "darwin 24.4.0", "region": "us-east-1"}}, "metrics_data": {"session_info": {"start_time": **********.723746, "end_time": **********.099112, "duration": 116.37536597251892, "total_turns": 4, "user_turns": 1, "assistant_turns": 3}, "speech_quality_metrics": {"wer_scores": [], "cer_scores": [], "per_scores": [], "pesq_scores": [1.5, 1.5, 1.5], "stoi_scores": [1.0, 1.0, 1.0], "estoi_scores": [1.0, 1.0, 1.0], "mcd_scores": [10.0, 10.0, 10.0], "speaker_embedding_similarity": [0.0, 0.0, 0.0], "eer_scores": [0.5, 0.5, 0.5], "duration_rhythm_deviation": [1.0, 1.0, 1.0], "energy_contours": [0.0, 0.0, 0.0]}, "latency_metrics": {"asr_latency": [1.1823713779449463, 5.176420569419861, 0.4137941122055054], "tts_latency": [1.5764951705932617, 6.9018940925598145, 0.5517254829406738], "turn_latency": [3.9412379264831543, 17.254735231399536, 1.3793137073516846], "barge_in_response_time": [], "first_token_latency": [1.1823713779449463, 5.176420569419861, 0.4137941122055054], "end_to_end_latency": [3.9412379264831543, 17.254735231399536, 1.3793137073516846]}, "single_turn_metrics": {"intent_accuracy": [], "slot_f1_scores": [], "response_appropriateness_score": [], "asr_confidence_scores": [], "usr_metric": []}, "multi_turn_metrics": {"task_success_rate": [], "conversation_length": [], "repetition_rate": []}, "no_ground_truth_metrics": {"toxicity_detection": [], "interruption_drop_offs": [], "user_satisfaction_proxy": []}, "rag_evaluation_metrics": {"retrieval_quality": {"retrieval_precision": [0.6666666666666666], "retrieval_recall": [0.29411764705882354], "retrieval_f1_score": [0.40816326530612246], "retrieval_relevance_scores": [1.9283559322357178, 1.9284131526947021, 1.933514952659607], "retrieval_diversity": [0.7484094852515906], "retrieval_coverage": [1.0], "retrieval_latency": [0.0], "retrieval_rank_accuracy": []}, "context_utilization": {"context_utilization_rate": [0.6669019054340155, 0.6669019054340155], "context_relevance_score": [0.3169192671775818, 0.6060797572135925], "context_coherence": [1.0, 1.0], "context_integration_quality": [0.6584596335887909, 0.8030398786067963], "context_window_efficiency": [0.230712890625, 0.230712890625], "context_overflow_rate": [0.0, 0.0]}, "context_awareness": {"context_awareness_score": [], "conversation_continuity_score": [], "response_relevance_score": [], "topic_coherence_score": [], "memory_retention_score": [], "adaptive_response_score": []}, "knowledge_base_quality": {"kb_coverage_score": [], "kb_freshness_score": [], "kb_consistency_score": [], "kb_completeness_score": [], "kb_accuracy_score": []}, "rag_generation_quality": {"rag_response_relevance": [0.0, 0.5513652563095093, 0.4976760149002075], "rag_factual_accuracy": [0.0, 0.5, 0.5], "rag_coherence_score": [0.3333333333333333, 0.0, 0.0], "rag_consistency_score": [0.0, 1.0, 1.0], "rag_creativity_score": [0.0, 0.5, 0.5], "rag_adaptation_quality": [0.0, 0.5, 0.5]}, "rag_efficiency": {"rag_total_latency": [0.0, 0.0, 0.0], "rag_retrieval_efficiency": [], "rag_generation_efficiency": [], "rag_token_efficiency": [0, 0, 0], "rag_cost_efficiency": [], "rag_throughput": []}, "rag_error_analysis": {"rag_hallucination_rate": [], "rag_context_misuse": [], "rag_retrieval_failures": [], "rag_generation_failures": [], "rag_fallback_usage": []}, "rag_user_experience": {"rag_response_helpfulness": [], "rag_information_quality": [], "rag_answer_completeness": [], "rag_user_satisfaction": [], "rag_trust_score": []}, "rag_conversation_flow": {"rag_context_continuity": [], "rag_topic_coherence": [], "rag_question_relevance": [], "rag_follow_up_quality": [], "rag_conversation_depth": []}, "rag_model_performance": {"rag_embedding_quality": [], "rag_similarity_accuracy": [], "rag_ranking_quality": [], "rag_reranking_effectiveness": [], "rag_fusion_quality": []}, "question_alignment_metrics": {"question_jd_alignment": [0.7124252915382385, 0.19182704389095306, 0.145681694149971, 0.2799200713634491, 0.23472923040390015, 0.8432983756065369, 0.25899335741996765, 0.10792477428913116, 0.11069104075431824, 0.08977054804563522, 0.1664440780878067, 0.3635143041610718], "question_resume_alignment": [0.434613972902298, 0.26225176453590393, 0.2033916711807251, 0.23406749963760376, 0.3076489269733429, 0.27897658944129944, 0.11567705869674683, 0.007767674513161182, 0.09156352281570435, 0.09054194390773773, 0.28562361001968384, 0.3041572868824005], "question_technical_relevance": [1.0, 0.5, 0.5, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 0.5, 0.5], "question_experience_relevance": [0.8, 0.8, 0.5, 0.8, 0.8, 0.8, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5], "question_role_specificity": [1.0, 0.5, 0.5, 0.8, 0.5, 1.0, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5], "question_difficulty_appropriateness": [1.0, 0.5, 0.5, 0.5, 1.0, 0.8, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5]}}, "conversation_flow": [{"turn_number": 1, "type": "assistant", "timestamp": 1758232771.74314, "response_time": 33.01939296722412, "response": "Hello! I'm your AI interviewer today. Thank you for joining us for this L1 screening interview for the Senior Generative AI Engineer position. I'll be asking you some questions about your background and experience. Let's begin with our first question: Can you walk me through your experience with building and deploying large language models?", "context_used": "interview_context"}, {"turn_number": 2, "type": "user", "timestamp": 1758232800.3708081, "turn_latency": 28.627668142318726, "transcript": "I don't have any experience to build large language model. But I have used for LLM application to build AI agents.", "audio_metrics": {"snr": 0, "audio_level": 0, "dynamic_range": 0, "sample_count": 0}, "speech_metrics": {"word_count": 21, "char_count": 114, "speech_rate": 1.8421052631578947, "vocabulary_diversity": 0.8095238095238095, "avg_sentence_length": 7.0, "estimated_duration": 11.4}}, {"turn_number": 3, "type": "assistant", "timestamp": 1758232817.249152, "response_time": 16.8783438205719, "response": "Thank you for sharing that. Since you've used large language models for building AI agents, can you tell me about a project where you implemented AI agents using large language models, and what role those models played in the project?", "context_used": "interview_context"}, {"turn_number": 4, "type": "assistant", "timestamp": 1758232823.073806, "response_time": 5.8246541023254395, "response": "Thank you for that information. Based on your experience with building AI agents, can you discuss your work on the multichannel conversational AI agent you built for Contact Center Solutions using AWS Bedrock with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>? How did you approach prompt engineering and knowledge base management in that project?", "context_used": "interview_context"}], "summary_statistics": {"pesq_avg": 1.5, "pesq_min": 1.5, "pesq_max": 1.5, "stoi_avg": 1.0, "stoi_min": 1.0, "stoi_max": 1.0, "asr_latency_avg": 2.2575286865234374, "asr_latency_p95": 5.176420569419861, "tts_latency_avg": 3.0100382486979167, "tts_latency_p95": 6.9018940925598145, "asr_confidence_avg": 0, "rag_response_relevance_avg": 0.34968042373657227, "rag_response_relevance_min": 0.0, "rag_response_relevance_max": 0.5513652563095093, "rag_factual_accuracy_avg": 0.3333333333333333, "rag_factual_accuracy_min": 0.0, "rag_factual_accuracy_max": 0.5, "rag_coherence_avg": 0.1111111111111111, "rag_coherence_min": 0.0, "rag_coherence_max": 0.3333333333333333, "rag_context_utilization_avg": 0.6669019054340155, "rag_context_utilization_min": 0.6669019054340155, "rag_context_utilization_max": 0.6669019054340155, "rag_hallucination_rate_avg": 0, "rag_hallucination_rate_max": 0, "rag_retrieval_failure_rate": 0, "rag_generation_failure_rate": 0}}}