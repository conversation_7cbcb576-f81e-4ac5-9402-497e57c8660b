{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech"}, "interview_info": {"session_id": "fd6b8453", "timestamp": **********, "date": "2025-09-18T16:16:34.611571", "role_type": "technical", "jd_file": "JD_Generative_AI.txt", "resume_file": "Resume_VarunSoni.pdf"}, "interview_metrics": {"total_questions_asked": 0, "total_user_responses": 1, "session_duration": 90.26200819015503, "user_frustration_level": 0}, "conversation_summary": {"total_questions_asked": 0, "total_user_responses": 1, "questions_list": [], "user_responses": ["Hello, my name is <PERSON><PERSON><PERSON>. I have overall five years of experience and I'm working to generative AI"], "conversation_flow": "technical Interview"}, "candidate_assessment": {"total_questions": 0, "total_responses": 1, "response_completeness": "Complete", "technical_skills": ["Ai"], "experience_level": "Junior", "communication_skills": "Good", "response_quality": "High", "overall_rating": "Good", "role_fit": "Strong Fit", "recommendations": ["Limited interaction - consider extending interview"]}, "technical_evaluation": {"experience_level": "Junior", "technical_skills": ["Ai"], "communication_skills": "Good", "response_quality": "High"}, "recommendations": {"next_steps": ["Limited interaction - consider extending interview"], "interview_rating": "Good", "fit_assessment": "Strong Fit"}, "files_generated": {"audio_recording": "data/outputs/recordings/recording_realtime_**********.wav", "transcript": "data/outputs/transcripts/transcript_realtime_**********.json", "metrics": "data/outputs/evaluations/metrics/metrics_realtime_**********.json", "summary": "data/outputs/evaluations/summaries/summary_realtime_**********.json"}}