{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech"}, "interview_info": {"session_id": "9b026764", "timestamp": **********, "date": "2025-09-18T15:54:51.163326", "role_type": "technical", "jd_file": "JD_Generative_AI.txt", "resume_file": "Resume_VarunSoni.pdf"}, "interview_metrics": {"total_questions_asked": 0, "total_user_responses": 3, "session_duration": 82.9239330291748, "user_frustration_level": 0}, "conversation_summary": {"total_questions_asked": 0, "total_user_responses": 3, "questions_list": [], "user_responses": ["Uh...", "Um, I'm having a little...", "5 year of working as a data analyst."], "conversation_flow": "technical Interview"}, "candidate_assessment": {"total_questions": 0, "total_responses": 3, "response_completeness": "Partial", "technical_skills": [], "experience_level": "Unknown", "communication_skills": "Fair", "response_quality": "Low", "overall_rating": "Fair", "role_fit": "Poor Fit", "recommendations": ["No technical skills mentioned - assess technical background"]}, "technical_evaluation": {"experience_level": "Unknown", "technical_skills": [], "communication_skills": "Fair", "response_quality": "Low"}, "recommendations": {"next_steps": ["No technical skills mentioned - assess technical background"], "interview_rating": "Fair", "fit_assessment": "Poor Fit"}, "files_generated": {"audio_recording": "data/outputs/recordings/recording_realtime_**********.wav", "transcript": "data/outputs/transcripts/transcript_realtime_**********.json", "metrics": "data/outputs/evaluations/metrics/metrics_realtime_**********.json", "summary": "data/outputs/evaluations/summaries/summary_realtime_**********.json"}}