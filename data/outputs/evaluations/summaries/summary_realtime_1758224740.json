{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech"}, "interview_info": {"session_id": "1f0f65fa", "timestamp": **********, "date": "2025-09-18T15:45:40.201069", "role_type": "technical", "jd_file": "JD_Generative_AI.txt", "resume_file": "Resume_VarunSoni.pdf"}, "interview_metrics": {"total_questions_asked": 0, "total_user_responses": 3, "session_duration": 105.07643175125122, "user_frustration_level": 0}, "conversation_summary": {"total_questions_asked": 0, "total_user_responses": 3, "questions_list": [], "user_responses": ["My name is <PERSON><PERSON><PERSON>. I have experience in IT from...", "5 to...", "अलो!"], "conversation_flow": "technical Interview"}, "candidate_assessment": {"total_questions": 0, "total_responses": 3, "response_completeness": "Incomplete", "technical_skills": [], "experience_level": "Junior", "communication_skills": "Poor", "response_quality": "Low", "overall_rating": "Poor", "role_fit": "Poor Fit", "recommendations": ["No technical skills mentioned - assess technical background"]}, "technical_evaluation": {"experience_level": "Junior", "technical_skills": [], "communication_skills": "Poor", "response_quality": "Low"}, "recommendations": {"next_steps": ["No technical skills mentioned - assess technical background"], "interview_rating": "Poor", "fit_assessment": "Poor Fit"}, "files_generated": {"audio_recording": "data/outputs/recordings/recording_realtime_**********.wav", "transcript": "data/outputs/transcripts/transcript_realtime_**********.json", "metrics": "data/outputs/evaluations/metrics/metrics_realtime_**********.json", "summary": "data/outputs/evaluations/summaries/summary_realtime_**********.json"}}