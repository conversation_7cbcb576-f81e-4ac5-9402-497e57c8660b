{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech"}, "interview_info": {"session_id": "df54cb64", "timestamp": **********, "date": "2025-09-18T17:29:25.174844", "role_type": "technical", "jd_file": "JD_Generative_AI.txt", "resume_file": "Resume_VarunSoni.pdf"}, "interview_metrics": {"total_questions_asked": 0, "total_user_responses": 3, "session_duration": 158.7351689338684, "user_frustration_level": 0}, "conversation_summary": {"total_questions_asked": 0, "total_user_responses": 3, "questions_list": [], "user_responses": ["Yeah, I used a large language model for building a conversational AI agent and few of search-based rag-engine systems.", "क्योंकि तुम्हारी वोईस ब्रेकिंग है?", "We use the event-driven architecture, it's all just simply a task for interviewing both candidates and generate a question and the simple aspects for them."], "conversation_flow": "technical Interview"}, "candidate_assessment": {"total_questions": 0, "total_responses": 3, "response_completeness": "Very Complete", "technical_skills": ["Ai"], "experience_level": "Senior", "communication_skills": "Excellent", "response_quality": "High", "overall_rating": "Good", "role_fit": "Partial Fit", "recommendations": ["Limited technical discussion - focus on technical questions"]}, "technical_evaluation": {"experience_level": "Senior", "technical_skills": ["Ai"], "communication_skills": "Excellent", "response_quality": "High"}, "recommendations": {"next_steps": ["Limited technical discussion - focus on technical questions"], "interview_rating": "Good", "fit_assessment": "Partial Fit"}, "files_generated": {"audio_recording": "data/outputs/recordings/recording_realtime_**********.wav", "transcript": "data/outputs/transcripts/transcript_realtime_**********.json", "metrics": "data/outputs/evaluations/metrics/metrics_realtime_**********.json", "summary": "data/outputs/evaluations/summaries/summary_realtime_**********.json"}}