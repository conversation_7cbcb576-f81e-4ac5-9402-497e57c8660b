{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech"}, "interview_info": {"session_id": "14cb0296", "timestamp": **********, "date": "2025-09-18T17:56:09.232452", "role_type": "technical", "jd_file": "JD_Generative_AI.txt", "resume_file": "Resume_VarunSoni.pdf"}, "interview_metrics": {"total_questions_asked": 0, "total_user_responses": 1, "session_duration": 68.40500617027283, "user_frustration_level": 0}, "conversation_summary": {"total_questions_asked": 0, "total_user_responses": 1, "questions_list": [], "user_responses": ["Yeah, I worked on a conversational AI system to"], "conversation_flow": "technical Interview"}, "candidate_assessment": {"total_questions": 0, "total_responses": 1, "response_completeness": "Partial", "technical_skills": ["Ai"], "experience_level": "Unknown", "communication_skills": "Fair", "response_quality": "Medium", "overall_rating": "Fair", "role_fit": "Strong Fit", "recommendations": ["Limited interaction - consider extending interview"]}, "technical_evaluation": {"experience_level": "Unknown", "technical_skills": ["Ai"], "communication_skills": "Fair", "response_quality": "Medium"}, "recommendations": {"next_steps": ["Limited interaction - consider extending interview"], "interview_rating": "Fair", "fit_assessment": "Strong Fit"}, "files_generated": {"audio_recording": "data/outputs/recordings/recording_realtime_**********.wav", "transcript": "data/outputs/transcripts/transcript_realtime_**********.json", "metrics": "data/outputs/evaluations/metrics/metrics_realtime_**********.json", "summary": "data/outputs/evaluations/summaries/summary_realtime_**********.json"}}