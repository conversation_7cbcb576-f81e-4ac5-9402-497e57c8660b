{"model_identification": {"model_id": "openai-realtime-api", "model_name": "realtime-with-kb", "model_version": "1.0", "provider": "OpenAI", "model_type": "speech-to-speech"}, "interview_info": {"session_id": "0ba77992", "timestamp": **********, "date": "2025-09-18T18:00:55.129543", "role_type": "technical", "jd_file": "JD_Generative_AI.txt", "resume_file": "Resume_VarunSoni.pdf"}, "interview_metrics": {"total_questions_asked": 0, "total_user_responses": 1, "session_duration": 116.40594506263733, "user_frustration_level": 0}, "conversation_summary": {"total_questions_asked": 0, "total_user_responses": 1, "questions_list": [], "user_responses": ["I don't have any experience to build large language model. But I have used for LLM application to build AI agents."], "conversation_flow": "technical Interview"}, "candidate_assessment": {"total_questions": 0, "total_responses": 1, "response_completeness": "Very Complete", "technical_skills": ["Ai"], "experience_level": "Junior", "communication_skills": "Excellent", "response_quality": "High", "overall_rating": "Excellent", "role_fit": "Strong Fit", "recommendations": ["Limited interaction - consider extending interview"]}, "technical_evaluation": {"experience_level": "Junior", "technical_skills": ["Ai"], "communication_skills": "Excellent", "response_quality": "High"}, "recommendations": {"next_steps": ["Limited interaction - consider extending interview"], "interview_rating": "Excellent", "fit_assessment": "Strong Fit"}, "files_generated": {"audio_recording": "data/outputs/recordings/recording_realtime_**********.wav", "transcript": "data/outputs/transcripts/transcript_realtime_**********.json", "metrics": "data/outputs/evaluations/metrics/metrics_realtime_**********.json", "summary": "data/outputs/evaluations/summaries/summary_realtime_**********.json"}}